{"ast": null, "code": "export default {\n  props: {\n    type: {\n      type: String,\n      required: true //必须传值\n    }\n  }\n};", "map": {"version": 3, "names": ["props", "type", "String", "required"], "sources": ["src/components/Icon/index.vue"], "sourcesContent": ["<template>\r\n  <i class=\"iconfont icon-containe \" :class=\"'icon-' + type\"></i>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    props:{\r\n        type:{\r\n            type:String,\r\n            required:true,//必须传值\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n@import \"//at.alicdn.com/t/c/font_4881694_nkmt8qzpsz.css\";\r\n.custom-icon {\r\n    font-size: inherit !important;\r\n}\r\n</style>\r\n"], "mappings": "AAKA;EACAA,KAAA;IACAC,IAAA;MACAA,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}