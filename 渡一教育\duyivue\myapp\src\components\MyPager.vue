<template>
<!-- 分页组件
知识点：全局样式在ming.js中导入你设置的全局样式文件
 -->


 <!-- 只有大于1时才显示这个div -->
    <div class="pager-container" v-if="pageNumber>1">
        <!-- {{visblemin}}
        {{visblemax}} -->
        <a @click="handleClick(1)" v-show="curren>1" :class="{disabled:curren===1}">&lt;&lt;</a>
        <!-- <a href="" v-show="current>1" :class="{disabled:current===1}">&lt;&lt;</a>  -->
        <a  v-for="item in number" 
        :key="item" 
        :class="{active:item===curren}"
        @click="handleClick(item)"
        >
        {{item}}
        </a>
        <a  :class="{disabled:curren===pageNumber}" @click="handleClick(curren+1)">&gt;&gt;</a>
    </div>
</template>
<style scoped lang="less">
@import '@/style/var.less';
.pager-container{
    display: flex;
    justify-content: center;
    margin: 20px 0;
    a{
        color:@gray-dark;
        margin: 0 5px;
        &:hover{
            color:@orimary;
        }
        &.disabled{
            color:@gray-light;
            cursor: not-allowed;
        }
        &.active{
            color:@orimary;
            font-weight: bold;
            cursor: text;
        }
    }
}
</style>
<script>
export default {
props:{
    curren:{//当前页码
        type:Number,
        default:1
    },
    total:{//总量数据
        type:Number,
        default:0
    },
    limit:{//分页容量
        type:Number,
        default:10,
    },
    visbleNumber:{//可见页码数
        type:Number,
        default:10,
    }
},
computed:{
    //总页数
    pageNumber(){
        return Math.ceil(this.total/this.limit);//向上取整
    },
    number(){
        let nums=[];
        for(let i=this.visblemin;i<=this.visblemax;i++){
            nums.push(i)
        }
       return nums
    }
    ,
    //得到页码中最小的数字
    visblemin(){
      let min= this.curren-Math.floor(this.visbleNumber/2)
      if(min<1){
          min=1
      }
      return min

    },
    visblemax(){
        let max=this.visblemin+this.visbleNumber-1;
        if(max>this.pageNumber){
            max=this.pageNumber
        }
        return max
    }
},
methods:{
    handleClick(newpage){
        console.log(newpage);
        return this.$emit('handelchange', newpage)
        
    }
}
}
</script>
