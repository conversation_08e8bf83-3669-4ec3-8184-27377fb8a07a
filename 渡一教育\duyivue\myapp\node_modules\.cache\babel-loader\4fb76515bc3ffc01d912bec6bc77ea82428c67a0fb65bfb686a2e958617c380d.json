{"ast": null, "code": "import \"./banner\";\n// import \"./blog\"\nimport Mock from \"mockjs\";\n// Mock.setup({ timeout: \"1000-2000\" });", "map": {"version": 3, "names": ["<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/mock/index.js"], "sourcesContent": ["import \"./banner\";\r\n// import \"./blog\"\r\nimport Mock from \"mockjs\";\r\n// Mock.setup({ timeout: \"1000-2000\" });"], "mappings": "AAAA,OAAO,UAAU;AACjB;AACA,OAAOA,IAAI,MAAM,QAAQ;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}