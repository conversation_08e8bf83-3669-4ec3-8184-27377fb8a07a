{"ast": null, "code": "import showMessage from \"@/utils/showMessage.js\";\nimport request from \"./request\";\nexport async function getBanner() {\n  var res = await request.get(\"/api/banner\");\n}\ngetBanner().then(r => {\n  console.log(r);\n});", "map": {"version": 3, "names": ["showMessage", "request", "getBanner", "res", "get", "then", "r", "console", "log"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/api/banner.js"], "sourcesContent": ["import showMessage from \"@/utils/showMessage.js\";\r\nimport request from \"./request\";\r\n\r\nexport async function getBanner(){\r\n    var res = await request.get(\"/api/banner\")\r\n\r\n    \r\n}\r\ngetBanner().then((r)=>{\r\n    console.log(r);\r\n    \r\n})"], "mappings": "AAAA,OAAOA,WAAW,MAAM,wBAAwB;AAChD,OAAOC,OAAO,MAAM,WAAW;AAE/B,OAAO,eAAeC,SAASA,CAAA,EAAE;EAC7B,IAAIC,GAAG,GAAG,MAAMF,OAAO,CAACG,GAAG,CAAC,aAAa,CAAC;AAG9C;AACAF,SAAS,CAAC,CAAC,CAACG,IAAI,CAAEC,CAAC,IAAG;EAClBC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;AAElB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}