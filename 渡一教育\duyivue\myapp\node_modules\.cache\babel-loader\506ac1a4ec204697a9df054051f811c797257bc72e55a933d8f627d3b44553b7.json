{"ast": null, "code": "import showMessage from \"@/utils/showMessage.js\";\nimport axios from \"axios\";\nconst ins = axios.create();\nins.interceptors.response.use(function (resp) {\n  if (resp.data.code !== 0) {\n    showMessage(resp.data.msg, \"error\", 5000);\n    return null;\n  }\n});\nexport default ins;", "map": {"version": 3, "names": ["showMessage", "axios", "ins", "create", "interceptors", "response", "use", "resp", "data", "code", "msg"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/api/request.js"], "sourcesContent": ["import showMessage from \"@/utils/showMessage.js\";\r\nimport axios from \"axios\";\r\nconst ins=axios.create()\r\nins.interceptors.response.use(function (resp) { \r\n    if(resp.data.code!==0){\r\n        showMessage(resp.data.msg,\"error\",5000)\r\n        return null\r\n    }\r\n})\r\nexport default ins;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,wBAAwB;AAChD,OAAOC,KAAK,MAAM,OAAO;AACzB,MAAMC,GAAG,GAACD,KAAK,CAACE,MAAM,CAAC,CAAC;AACxBD,GAAG,CAACE,YAAY,CAACC,QAAQ,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;EAC1C,IAAGA,IAAI,CAACC,IAAI,CAACC,IAAI,KAAG,CAAC,EAAC;IAClBT,WAAW,CAACO,IAAI,CAACC,IAAI,CAACE,GAAG,EAAC,OAAO,EAAC,IAAI,CAAC;IACvC,OAAO,IAAI;EACf;AACJ,CAAC,CAAC;AACF,eAAeR,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}