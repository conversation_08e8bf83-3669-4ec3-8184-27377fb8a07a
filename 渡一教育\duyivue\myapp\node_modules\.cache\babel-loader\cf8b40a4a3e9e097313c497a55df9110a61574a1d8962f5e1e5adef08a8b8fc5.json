{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"home-container\"\n  }, [_c(\"ul\", _vm._l(_vm.banners, function (item) {\n    return _c(\"li\", {\n      key: item.id\n    }, [_c(\"CarouselitemVue\")], 1);\n  }), 0), _c(\"div\", {\n    staticClass: \"icon icon-xia\"\n  }, [_c(\"Icon\", {\n    attrs: {\n      type: \"fenye-shangyiye\"\n    }\n  })], 1), _c(\"div\", {\n    staticClass: \"icon icon-xia\"\n  }, [_c(\"Icon\", {\n    attrs: {\n      type: \"xiala-shang\"\n    }\n  })], 1)]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_l", "banners", "item", "key", "id", "attrs", "type", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/views/Home/index.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { staticClass: \"home-container\" }, [\n    _c(\n      \"ul\",\n      _vm._l(_vm.banners, function (item) {\n        return _c(\"li\", { key: item.id }, [_c(\"CarouselitemVue\")], 1)\n      }),\n      0\n    ),\n    _c(\n      \"div\",\n      { staticClass: \"icon icon-xia\" },\n      [_c(\"Icon\", { attrs: { type: \"fenye-shangyiye\" } })],\n      1\n    ),\n    _c(\n      \"div\",\n      { staticClass: \"icon icon-xia\" },\n      [_c(\"Icon\", { attrs: { type: \"xiala-shang\" } })],\n      1\n    ),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAClDF,EAAE,CACA,IAAI,EACJD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,OAAO,EAAE,UAAUC,IAAI,EAAE;IAClC,OAAOL,EAAE,CAAC,IAAI,EAAE;MAAEM,GAAG,EAAED,IAAI,CAACE;IAAG,CAAC,EAAE,CAACP,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/D,CAAC,CAAC,EACF,CACF,CAAC,EACDA,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CAACF,EAAE,CAAC,MAAM,EAAE;IAAEQ,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAkB;EAAE,CAAC,CAAC,CAAC,EACpD,CACF,CAAC,EACDT,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CAACF,EAAE,CAAC,MAAM,EAAE;IAAEQ,KAAK,EAAE;MAAEC,IAAI,EAAE;IAAc;EAAE,CAAC,CAAC,CAAC,EAChD,CACF,CAAC,CACF,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBZ,MAAM,CAACa,aAAa,GAAG,IAAI;AAE3B,SAASb,MAAM,EAAEY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}