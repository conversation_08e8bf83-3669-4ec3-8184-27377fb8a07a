{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nexport default {\n  props: {\n    curren: {\n      //当前页码\n      type: Number,\n      default: 1\n    },\n    total: {\n      //总量数据\n      type: Number,\n      default: 0\n    },\n    limit: {\n      //分页容量\n      type: Number,\n      default: 10\n    },\n    visbleNumber: {\n      //可见页码数\n      type: Number,\n      default: 10\n    }\n  },\n  computed: {\n    //总页数\n    pageNumber() {\n      return Math.ceil(this.total / this.limit); //向上取整\n    },\n    number() {\n      let nums = [];\n      for (let i = this.visblemin; i <= this.visblemax; i++) {\n        nums.push(i);\n      }\n      return nums;\n    },\n    //得到页码中最小的数字\n    visblemin() {\n      let min = this.curren - Math.floor(this.visbleNumber / 2);\n      if (min < 1) {\n        min = 1;\n      }\n      return min;\n    },\n    visblemax() {\n      let max = this.visblemin + this.visbleNumber - 1;\n      if (max > this.pageNumber) {\n        max = this.pageNumber;\n      }\n      return max;\n    }\n  },\n  methods: {\n    handleClick(newpage) {\n      console.log(newpage);\n      return this.$emit('handelchange', newpage);\n    }\n  }\n};", "map": {"version": 3, "names": ["props", "curren", "type", "Number", "default", "total", "limit", "visbleNumber", "computed", "pageNumber", "Math", "ceil", "number", "nums", "i", "visblemin", "visblemax", "push", "min", "floor", "max", "methods", "handleClick", "newpage", "console", "log", "$emit"], "sources": ["src/components/Pager/MyIndex.vue"], "sourcesContent": ["<template>\r\n<!-- 分页组件\r\n知识点：全局样式在ming.js中导入你设置的全局样式文件\r\n -->\r\n\r\n <!-- 只有大于1时才显示这个div -->\r\n    <div class=\"pager-container\" v-if=\"pageNumber>1\">\r\n        <!-- {{visblemin}}\r\n        {{visblemax}} -->\r\n        <a @click=\"handleClick(1)\" v-show=\"curren>1\" :class=\"{disabled:curren===1}\">&lt;&lt;</a>\r\n        <!-- <a href=\"\" v-show=\"current>1\" :class=\"{disabled:current===1}\">&lt;&lt;</a>  -->\r\n        <a  v-for=\"item in number\" \r\n        :key=\"item\" \r\n        :class=\"{active:item===curren}\"\r\n        @click=\"handleClick(item)\"\r\n        >\r\n        {{item}}\r\n        </a>\r\n        <a  :class=\"{disabled:curren===pageNumber}\" @click=\"handleClick(curren+1)\">&gt;&gt;</a>\r\n    </div>\r\n</template>\r\n<style scoped lang=\"less\">\r\n@import '@/style/var.less';\r\n.pager-container{\r\n    display: flex;\r\n    justify-content: center;\r\n    margin: 20px 0;\r\n    a{\r\n        color:@gray-dark;\r\n        margin: 0 5px;\r\n        &:hover{\r\n            color:@orimary;\r\n        }\r\n        &.disabled{\r\n            color:@gray-light;\r\n            cursor: not-allowed;\r\n        }\r\n        &.active{\r\n            color:@orimary;\r\n            font-weight: bold;\r\n            cursor: text;\r\n        }\r\n    }\r\n}\r\n</style>\r\n<script>\r\nexport default {\r\nprops:{\r\n    curren:{//当前页码\r\n        type:Number,\r\n        default:1\r\n    },\r\n    total:{//总量数据\r\n        type:Number,\r\n        default:0\r\n    },\r\n    limit:{//分页容量\r\n        type:Number,\r\n        default:10,\r\n    },\r\n    visbleNumber:{//可见页码数\r\n        type:Number,\r\n        default:10,\r\n    }\r\n},\r\ncomputed:{\r\n    //总页数\r\n    pageNumber(){\r\n        return Math.ceil(this.total/this.limit);//向上取整\r\n    },\r\n    number(){\r\n        let nums=[];\r\n        for(let i=this.visblemin;i<=this.visblemax;i++){\r\n            nums.push(i)\r\n        }\r\n       return nums\r\n    }\r\n    ,\r\n    //得到页码中最小的数字\r\n    visblemin(){\r\n      let min= this.curren-Math.floor(this.visbleNumber/2)\r\n      if(min<1){\r\n          min=1\r\n      }\r\n      return min\r\n\r\n    },\r\n    visblemax(){\r\n        let max=this.visblemin+this.visbleNumber-1;\r\n        if(max>this.pageNumber){\r\n            max=this.pageNumber\r\n        }\r\n        return max\r\n    }\r\n},\r\nmethods:{\r\n    handleClick(newpage){\r\n        console.log(newpage);\r\n        return this.$emit('handelchange', newpage)\r\n        \r\n    }\r\n}\r\n}\r\n</script>\r\n"], "mappings": ";AA8CA;EACAA,KAAA;IACAC,MAAA;MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,KAAA;MAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAG,YAAA;MAAA;MACAL,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAI,QAAA;IACA;IACAC,WAAA;MACA,OAAAC,IAAA,CAAAC,IAAA,MAAAN,KAAA,QAAAC,KAAA;IACA;IACAM,OAAA;MACA,IAAAC,IAAA;MACA,SAAAC,CAAA,QAAAC,SAAA,EAAAD,CAAA,SAAAE,SAAA,EAAAF,CAAA;QACAD,IAAA,CAAAI,IAAA,CAAAH,CAAA;MACA;MACA,OAAAD,IAAA;IACA;IAEA;IACAE,UAAA;MACA,IAAAG,GAAA,QAAAjB,MAAA,GAAAS,IAAA,CAAAS,KAAA,MAAAZ,YAAA;MACA,IAAAW,GAAA;QACAA,GAAA;MACA;MACA,OAAAA,GAAA;IAEA;IACAF,UAAA;MACA,IAAAI,GAAA,QAAAL,SAAA,QAAAR,YAAA;MACA,IAAAa,GAAA,QAAAX,UAAA;QACAW,GAAA,QAAAX,UAAA;MACA;MACA,OAAAW,GAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAAC,OAAA;MACAC,OAAA,CAAAC,GAAA,CAAAF,OAAA;MACA,YAAAG,KAAA,iBAAAH,OAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}