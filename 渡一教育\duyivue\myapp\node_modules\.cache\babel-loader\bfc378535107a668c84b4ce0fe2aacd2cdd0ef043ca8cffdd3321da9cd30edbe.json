{"ast": null, "code": "// import {showMessage} from \"@/utils/index.js\"\nexport default {\n  data() {\n    return {\n      banners: \"\"\n    };\n  },\n  methods: {\n    btn() {}\n  }\n};", "map": {"version": 3, "names": ["data", "banners", "methods", "btn"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\" ref=\"container\">\r\n   \r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import {showMessage} from \"@/utils/index.js\"\r\nexport default {\r\n  data(){\r\n    return{\r\n      banners:\"\"\r\n    }\r\n},\r\nmethods:{\r\n    btn(){\r\n     \r\n    }\r\n}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n\r\n</style>"], "mappings": "AAOA;AACA;EACAA,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,GAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}