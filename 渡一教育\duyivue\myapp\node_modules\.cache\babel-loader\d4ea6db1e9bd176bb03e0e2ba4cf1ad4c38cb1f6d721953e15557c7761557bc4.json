{"ast": null, "code": "import Vue from 'vue';\nimport Vuex from 'vuex';\nVue.use(Vuex);\nexport default new Vuex.Store({\n  state: {},\n  getters: {},\n  mutations: {},\n  actions: {},\n  modules: {}\n});", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "use", "Store", "state", "getters", "mutations", "actions", "modules"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\n\nVue.use(Vuex)\n\nexport default new Vuex.Store({\n  state: {\n  },\n  getters: {\n  },\n  mutations: {\n  },\n  actions: {\n  },\n  modules: {\n  }\n})\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AAEvBD,GAAG,CAACE,GAAG,CAACD,IAAI,CAAC;AAEb,eAAe,IAAIA,IAAI,CAACE,KAAK,CAAC;EAC5BC,KAAK,EAAE,CACP,CAAC;EACDC,OAAO,EAAE,CACT,CAAC;EACDC,SAAS,EAAE,CACX,CAAC;EACDC,OAAO,EAAE,CACT,CAAC;EACDC,OAAO,EAAE,CACT;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}