{"ast": null, "code": "import { getBanner } from \"../../api/banner.js\";\nimport CarouselitemVue from \"./Carouselitem.vue\";\nexport default {\n  components: {\n    CarouselitemVue\n  },\n  data() {\n    return {\n      banners: []\n      // container:null,\n    };\n  },\n  async created() {\n    this.banners = await getBanner();\n  }\n};", "map": {"version": 3, "names": ["getBanner", "CarouselitemVue", "components", "data", "banners", "created"], "sources": ["src/views/Home/index.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"container\" v-if=\"banners.length > 0\">\r\n    <ul>\r\n      <li v-for=\"item in banners\" :key=\"item.id\">\r\n        <img :src=\"item.img\" alt=\"\" />\r\n        <h2>{{ item.title }}</h2>\r\n      </li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getBanner } from \"../../api/banner.js\";\r\nimport CarouselitemVue from \"./Carouselitem.vue\";\r\n\r\nexport default {\r\n  components: {\r\n    CarouselitemVue,\r\n  },\r\n  data() {\r\n    return {\r\n      banners: [],\r\n      // container:null,\r\n    };\r\n  },\r\n  async created() {\r\n    this.banners = await getBanner();\r\n  },\r\n};\r\n</script>\r\n\r\n<style>\r\n</style>"], "mappings": "AAYA,SAAAA,SAAA;AACA,OAAAC,eAAA;AAEA;EACAC,UAAA;IACAD;EACA;EACAE,KAAA;IACA;MACAC,OAAA;MACA;IACA;EACA;EACA,MAAAC,QAAA;IACA,KAAAD,OAAA,SAAAJ,SAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}