{"ast": null, "code": "import { getBanner } from \"../api/banner.js\";\nexport default {\n  data() {\n    return {\n      banners: []\n    };\n  },\n  methods: {\n    btn() {}\n  }\n};", "map": {"version": 3, "names": ["getBanner", "data", "banners", "methods", "btn"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\" ref=\"container\">\r\n   <ul>\r\n     <ul>\r\n        <li v-for=\"item in banners\" :key=\"item.id\">\r\n            <img :src=\"item.img\" alt=\"\">\r\n            <h2>{{item.title}}</h2>\r\n        </li>\r\n    </ul>\r\n   </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getBanner} from \"../api/banner.js\"\r\nexport default {\r\n  data(){\r\n    return{\r\n      banners:[]\r\n    }\r\n},\r\nmethods:{\r\n    btn(){\r\n     \r\n    },\r\n  \r\n}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n\r\n</style>"], "mappings": "AAcA,SAAAA,SAAA;AACA;EACAC,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,GAEA;EAEA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}