{"ast": null, "code": "import showMessage from \"@/utils/showMessage.js\";\nimport request from \"./request\";\nexport async function getBanner() {\n  var res = await request.get(\"/api/banner\");\n  console.log(res.data);\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n  console.log(res.data.msg);\n  showMessage(res.data.msg, \"info\");\n}\ngetBanner().then(r => {\n  console.log(r);\n});", "map": {"version": 3, "names": ["showMessage", "request", "getBanner", "res", "get", "console", "log", "data", "code", "msg", "then", "r"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/api/banner.js"], "sourcesContent": ["import showMessage from \"@/utils/showMessage.js\";\r\nimport request from \"./request\";\r\n\r\nexport async function getBanner(){\r\n    var res = await request.get(\"/api/banner\")\r\n    console.log(res.data);\r\n    if(res.data.code===0){\r\n        return res.data.data\r\n    }\r\n    console.log(res.data.msg);\r\n    \r\n    showMessage(res.data.msg,\"info\")\r\n    \r\n}\r\ngetBanner().then((r)=>{\r\n    console.log(r);\r\n    \r\n})"], "mappings": "AAAA,OAAOA,WAAW,MAAM,wBAAwB;AAChD,OAAOC,OAAO,MAAM,WAAW;AAE/B,OAAO,eAAeC,SAASA,CAAA,EAAE;EAC7B,IAAIC,GAAG,GAAG,MAAMF,OAAO,CAACG,GAAG,CAAC,aAAa,CAAC;EAC1CC,OAAO,CAACC,GAAG,CAACH,GAAG,CAACI,IAAI,CAAC;EACrB,IAAGJ,GAAG,CAACI,IAAI,CAACC,IAAI,KAAG,CAAC,EAAC;IACjB,OAAOL,GAAG,CAACI,IAAI,CAACA,IAAI;EACxB;EACAF,OAAO,CAACC,GAAG,CAACH,GAAG,CAACI,IAAI,CAACE,GAAG,CAAC;EAEzBT,WAAW,CAACG,GAAG,CAACI,IAAI,CAACE,GAAG,EAAC,MAAM,CAAC;AAEpC;AACAP,SAAS,CAAC,CAAC,CAACQ,IAAI,CAAEC,CAAC,IAAG;EAClBN,OAAO,CAACC,GAAG,CAACK,CAAC,CAAC;AAElB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}