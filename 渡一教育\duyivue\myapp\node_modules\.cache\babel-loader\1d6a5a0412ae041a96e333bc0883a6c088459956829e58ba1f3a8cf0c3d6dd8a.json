{"ast": null, "code": "export default {\n  data() {\n    return {\n      banners: []\n      // container:null,\n    };\n  },\n  asy\n};", "map": {"version": 3, "names": ["data", "banners", "asy"], "sources": ["src/views/Home/index.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"container\" v-if=\"banners.length>0\"> \r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\ndata() {\r\n  return {\r\n    banners:[],\r\n    // container:null,\r\n  }\r\n},\r\nasy\r\n}\r\n</script>\r\n\r\n<style>\r\n\r\n</style>"], "mappings": "AAOA;EACAA,KAAA;IACA;MACAC,OAAA;MACA;IACA;EACA;EACAC;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}