{"ast": null, "code": "// import {showMessage} from \"@/utils/index.js\"\nexport default {\n  data() {\n    return {};\n  },\n  methods: {\n    btn() {\n      // this.$sayHello()\n      this.$showMessage(\"测试一下\", \"success\", 2000);\n    }\n  }\n};", "map": {"version": 3, "names": ["data", "methods", "btn", "$showMessage"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\" ref=\"container\">\r\n   \r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import {showMessage} from \"@/utils/index.js\"\r\nexport default {\r\n  data(){\r\n    return{\r\n\r\n    }\r\n},\r\nmethods:{\r\n    btn(){\r\n        // this.$sayHello()\r\n        this.$showMessage(\"测试一下\",\"success\",2000)\r\n    }\r\n}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n\r\n</style>"], "mappings": "AAOA;AACA;EACAA,KAAA;IACA,QAEA;EACA;EACAC,OAAA;IACAC,IAAA;MACA;MACA,KAAAC,YAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}