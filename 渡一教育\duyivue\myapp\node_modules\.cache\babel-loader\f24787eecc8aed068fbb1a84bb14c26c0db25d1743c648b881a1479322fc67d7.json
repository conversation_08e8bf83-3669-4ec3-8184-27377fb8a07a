{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport '@/style/global.less';\nVue.config.productionTip = false;\nnew Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "config", "productionTip", "render", "h", "$mount"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/main.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport '@/style/global.less'\r\n\r\nVue.config.productionTip = false\r\n\r\nnew Vue({\r\n  router,\r\n  store,\r\n  render: h => h(App)\r\n}).$mount('#app')\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAO,qBAAqB;AAE5BH,GAAG,CAACI,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIL,GAAG,CAAC;EACNE,MAAM;EACNC,KAAK;EACLG,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACN,GAAG;AACpB,CAAC,CAAC,CAACO,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}