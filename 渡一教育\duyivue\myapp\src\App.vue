<template>
  <div id="app">
    <h1>{{ title }}</h1>
    <p>{{ message }}</p>
    <button @click="changeMessage">点击改变消息</button>

    <!-- 测试组件 -->
    <div class="test-component">
      <h2>组件测试</h2>
      <ul>
        <li v-for="item in items" :key="item.id">
          {{ item.name }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: "App",
  data() {
    return {
      title: "Vue组件测试",
      message: "这是一个测试组件",
      items: [
        { id: 1, name: "项目1" },
        { id: 2, name: "项目2" },
        { id: 3, name: "项目3" },
      ],
    };
  },
  methods: {
    changeMessage() {
      this.message = "消息已改变！时间：" + new Date().toLocaleTimeString();
    },
  },
};
</script>

<style>
#app {
  font-family: Arial, sans-serif;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: #42b983;
  text-align: center;
}

.test-component {
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

button {
  background-color: #42b983;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #369870;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  background-color: white;
  margin: 5px 0;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #42b983;
}
</style>