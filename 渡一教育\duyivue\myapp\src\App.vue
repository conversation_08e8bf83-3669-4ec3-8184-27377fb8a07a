<template>
<!-- 
父组件可以影响子组件的根元素，
1.在style中导入阿里巴巴字体图标库
2.然后根据他提供的数据来写组件
-->
  <div>
    <MyPager :curren="curren" :total="300" :limit="10" @handelchange="handelchange"></MyPager>
  </div>
</template>
<script>
import MyPager from "@/components/Pager/MyIndex.vue";
export default {
  setup() {

  },
  components:{
   MyPager,
  },
  data(){
    return{
      url:"/img/vatar.jpg",
      curren:2,
    }
  },
  methods:{
    handelchange(newpage){
      if(newpage<1){
        newpage=1
      }
      if(newpage>this.pageNumber){
        newpage=this.pageNumber
      }
      if(newpage===this.curren){
        return
      }
    console.log("改变了",newpage);
    this.curren=newpage
    
  }
  }
}
</script>