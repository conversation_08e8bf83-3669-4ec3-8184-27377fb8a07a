{"ast": null, "code": "import showMessage from \"@/utils/showMessage.js\";\nimport axireqos from \"axios\";\nconst ins = axios.create();\nins.interceptors.response.use(function (resp) {\n  console.log(resp.data + \"sdushdudsg\"); //拦截器在你请求前先运行这个函数\n  return resp.data;\n});\nexport async function getBanner() {\n  var res = await ins.get(\"/api/banner\");\n  console.log(res.data);\n  if (res.data.code === 0) {\n    return res.data.data;\n  }\n  console.log(res.data.msg);\n  showMessage(res.data.msg, \"info\");\n}\ngetBanner().then(r => {\n  console.log(r);\n});", "map": {"version": 3, "names": ["showMessage", "axireqos", "ins", "axios", "create", "interceptors", "response", "use", "resp", "console", "log", "data", "getBanner", "res", "get", "code", "msg", "then", "r"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/api/banner.js"], "sourcesContent": ["import showMessage from \"@/utils/showMessage.js\";\r\nimport axireqos from \"axios\";\r\nconst ins=axios.create()\r\nins.interceptors.response.use(function (resp) { \r\n    console.log(resp.data+\"sdushdudsg\");//拦截器在你请求前先运行这个函数\r\n    return resp.data\r\n})\r\nexport async function getBanner(){\r\n    var res = await ins.get(\"/api/banner\")\r\n    console.log(res.data);\r\n    if(res.data.code===0){\r\n        return res.data.data\r\n    }\r\n    console.log(res.data.msg);\r\n    \r\n    showMessage(res.data.msg,\"info\")\r\n    \r\n}\r\ngetBanner().then((r)=>{\r\n    console.log(r);\r\n    \r\n})"], "mappings": "AAAA,OAAOA,WAAW,MAAM,wBAAwB;AAChD,OAAOC,QAAQ,MAAM,OAAO;AAC5B,MAAMC,GAAG,GAACC,KAAK,CAACC,MAAM,CAAC,CAAC;AACxBF,GAAG,CAACG,YAAY,CAACC,QAAQ,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;EAC1CC,OAAO,CAACC,GAAG,CAACF,IAAI,CAACG,IAAI,GAAC,YAAY,CAAC,CAAC;EACpC,OAAOH,IAAI,CAACG,IAAI;AACpB,CAAC,CAAC;AACF,OAAO,eAAeC,SAASA,CAAA,EAAE;EAC7B,IAAIC,GAAG,GAAG,MAAMX,GAAG,CAACY,GAAG,CAAC,aAAa,CAAC;EACtCL,OAAO,CAACC,GAAG,CAACG,GAAG,CAACF,IAAI,CAAC;EACrB,IAAGE,GAAG,CAACF,IAAI,CAACI,IAAI,KAAG,CAAC,EAAC;IACjB,OAAOF,GAAG,CAACF,IAAI,CAACA,IAAI;EACxB;EACAF,OAAO,CAACC,GAAG,CAACG,GAAG,CAACF,IAAI,CAACK,GAAG,CAAC;EAEzBhB,WAAW,CAACa,GAAG,CAACF,IAAI,CAACK,GAAG,EAAC,MAAM,CAAC;AAEpC;AACAJ,SAAS,CAAC,CAAC,CAACK,IAAI,CAAEC,CAAC,IAAG;EAClBT,OAAO,CAACC,GAAG,CAACQ,CAAC,CAAC;AAElB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}