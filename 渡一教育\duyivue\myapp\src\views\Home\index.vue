<template>
  <div class="home-container">
    <ul>
      <li v-for="item in banners" :key="item.id">
       <CarouselitemVue></CarouselitemVue>
      </li>
    </ul>
     <div class="icon icon-shang">
        <Icon type="xiala-shang"></Icon>
    </div>
    <div class="icon icon-xia">
        <Icon type="fenye-shangyiye"></Icon>
    </div>
    <ul class="indicator">
        <li v-for="item in banners" :key="item.id"></li>
    </ul>
  </div>
</template>

<script>
import { getBanner } from "../../api/banner.js";
import CarouselitemVue from "./Carouselitem.vue";
import Icon from "@/components/Icon/index.vue";
export default {
  components: {
    CarouselitemVue,
    Icon,
  },
  data() {
    return {
      banners: [],
      // container:null,
    };
  },
  async created() {
    this.banners = await getBanner();
  },
};
</script>

<style scoped lang="less">
@import "@/style/mixin.less";
.home-container { 
    width: 100%;
    height: 100%;
    position: relative;
    ul{
        margin: 0;
    }

    
}
.icon{
    .self-center();
    font-size: 30px;
    &.icon-shang{
        top:10px;
    }
    &.icon-xia{
        top:auto;
        bottom:10px;
    }
    

}
</style>