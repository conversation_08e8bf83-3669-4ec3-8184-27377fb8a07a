{"ast": null, "code": "import { getBanner } from \"../../api/banner.js\";\nimport CarouselitemVue from \"./Carouselitem.vue\";\nimport Icon from \"@/components/Icon/index.vue\";\nexport default {\n  components: {\n    CarouselitemVue,\n    Icon\n  },\n  data() {\n    return {\n      banners: []\n      // container:null,\n    };\n  },\n  async created() {\n    this.banners = await getBanner();\n  }\n};", "map": {"version": 3, "names": ["getBanner", "CarouselitemVue", "Icon", "components", "data", "banners", "created"], "sources": ["src/views/Home/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\">\r\n    <ul>\r\n      <li v-for=\"item in banners\" :key=\"item.id\">\r\n       <CarouselitemVue></CarouselitemVue>\r\n      </li>\r\n    </ul>\r\n     <div class=\"icon icon-shang\">\r\n        <Icon type=\"xiala-shang\"></Icon>\r\n    </div>\r\n    <div class=\"icon icon-xia\">\r\n        <Icon type=\"fenye-shangyiye\"></Icon>\r\n    </div>\r\n    <ul class=\"indicator\">\r\n        <li v-for=\"item in banners\" :key=\"item.id\"></li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getBanner } from \"../../api/banner.js\";\r\nimport CarouselitemVue from \"./Carouselitem.vue\";\r\nimport Icon from \"@/components/Icon/index.vue\";\r\nexport default {\r\n  components: {\r\n    CarouselitemVue,\r\n    Icon,\r\n  },\r\n  data() {\r\n    return {\r\n      banners: [],\r\n      // container:null,\r\n    };\r\n  },\r\n  async created() {\r\n    this.banners = await getBanner();\r\n  },\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n@import \"@/style/m\"\r\n.home-container { \r\n    width: 100%;\r\n    height: 100%;\r\n    position: relative;\r\n    ul{\r\n        margin: 0;\r\n    }\r\n\r\n    \r\n}\r\n.icon{\r\n    position: absolute;\r\n    \r\n\r\n}\r\n</style>"], "mappings": "AAoBA,SAAAA,SAAA;AACA,OAAAC,eAAA;AACA,OAAAC,IAAA;AACA;EACAC,UAAA;IACAF,eAAA;IACAC;EACA;EACAE,KAAA;IACA;MACAC,OAAA;MACA;IACA;EACA;EACA,MAAAC,QAAA;IACA,KAAAD,OAAA,SAAAL,SAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}