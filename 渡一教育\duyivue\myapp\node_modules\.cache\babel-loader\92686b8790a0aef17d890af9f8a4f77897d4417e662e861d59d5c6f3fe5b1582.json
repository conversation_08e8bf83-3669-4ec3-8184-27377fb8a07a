{"ast": null, "code": "export default {\n  name: \"App\",\n  data() {\n    return {\n      title: \"Vue组件测试\",\n      message: \"这是一个测试组件\",\n      items: [{\n        id: 1,\n        name: \"项目1\"\n      }, {\n        id: 2,\n        name: \"项目2\"\n      }, {\n        id: 3,\n        name: \"项目3\"\n      }]\n    };\n  },\n  methods: {\n    changeMessage() {\n      this.message = \"消息已改变！时间：\" + new Date().toLocaleTimeString();\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "data", "title", "message", "items", "id", "methods", "changeMessage", "Date", "toLocaleTimeString"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <h1>{{ title }}</h1>\n    <p>{{ message }}</p>\n    <button @click=\"changeMessage\">点击改变消息</button>\n\n    <!-- 测试组件 -->\n    <div class=\"test-component\">\n      <h2>组件测试</h2>\n      <ul>\n        <li v-for=\"item in items\" :key=\"item.id\">\n          {{ item.name }}\n        </li>\n      </ul>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"App\",\n  data() {\n    return {\n      title: \"Vue组件测试\",\n      message: \"这是一个测试组件\",\n      items: [\n        { id: 1, name: \"项目1\" },\n        { id: 2, name: \"项目2\" },\n        { id: 3, name: \"项目3\" },\n      ],\n    };\n  },\n  methods: {\n    changeMessage() {\n      this.message = \"消息已改变！时间：\" + new Date().toLocaleTimeString();\n    },\n  },\n};\n</script>\n\n<style>\n#app {\n  font-family: Arial, sans-serif;\n  max-width: 600px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\nh1 {\n  color: #42b983;\n  text-align: center;\n}\n\n.test-component {\n  background-color: #f5f5f5;\n  padding: 20px;\n  border-radius: 8px;\n  margin-top: 20px;\n}\n\nbutton {\n  background-color: #42b983;\n  color: white;\n  border: none;\n  padding: 10px 20px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\nbutton:hover {\n  background-color: #369870;\n}\n\nul {\n  list-style-type: none;\n  padding: 0;\n}\n\nli {\n  background-color: white;\n  margin: 5px 0;\n  padding: 10px;\n  border-radius: 4px;\n  border-left: 4px solid #42b983;\n}\n</style>"], "mappings": "AAmBA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,KAAA;MACAC,OAAA;MACAC,KAAA,GACA;QAAAC,EAAA;QAAAL,IAAA;MAAA,GACA;QAAAK,EAAA;QAAAL,IAAA;MAAA,GACA;QAAAK,EAAA;QAAAL,IAAA;MAAA;IAEA;EACA;EACAM,OAAA;IACAC,cAAA;MACA,KAAAJ,OAAA,qBAAAK,IAAA,GAAAC,kBAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}