{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\n// import App from '../App.vue'\n\nVue.use(VueRouter);\nconst routes = [{\n  path: '/',\n  name: 'home',\n  component: () => import('@/views/Home/index')\n}, {\n  path: '/blog',\n  name: 'Blog',\n  component: () => import('@/views/Blog.vue')\n}, {\n  path: '/contact',\n  name: 'Contact',\n  component: () => import('@/views/About.vue')\n}, {\n  path: '/message',\n  name: 'Message',\n  component: () => import('@/views/Project.vue')\n}, {\n  path: '/about',\n  name: 'about',\n  // route level code-splitting\n  // this generates a separate chunk (about.[hash].js) for this route\n  // which is lazy-loaded when the route is visited.\n  component: () => import(/* webpackChunkName: \"about\" */'../views/AboutView.vue')\n}];\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use", "routes", "path", "name", "component", "router", "mode", "base", "process", "env", "BASE_URL"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\n// import App from '../App.vue'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    name: 'home',\n    component: ()=>import('@/views/Home/index')\n  },\n  {\n    path: '/blog',\n    name: 'Blog',\n    component: () => import('@/views/Blog.vue')\n  },\n  {\n    path: '/contact',\n    name: 'Contact',\n    component: () => import('@/views/About.vue')\n  },\n  {\n    path: '/message',\n    name: 'Message',\n    component: () => import('@/views/Project.vue')\n  },\n\n  {\n    path: '/about',\n    name: 'about',\n    // route level code-splitting\n    // this generates a separate chunk (about.[hash].js) for this route\n    // which is lazy-loaded when the route is visited.\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/AboutView.vue')\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\nexport default router\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC;;AAEAD,GAAG,CAACE,GAAG,CAACD,SAAS,CAAC;AAElB,MAAME,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAI,MAAM,CAAC,oBAAoB;AAC5C,CAAC,EACD;EACEF,IAAI,EAAE,OAAO;EACbC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,kBAAkB;AAC5C,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB;AAC7C,CAAC,EACD;EACEF,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,qBAAqB;AAC/C,CAAC,EAED;EACEF,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACb;EACA;EACA;EACAC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,wBAAwB;AAClF,CAAC,CACF;AAED,MAAMC,MAAM,GAAG,IAAIN,SAAS,CAAC;EAC3BO,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BT;AACF,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}