{"ast": null, "code": "import { getBanner } from \"../api/banner.js\";\nexport default {\n  data() {\n    return {\n      banners: []\n    };\n  },\n  methods: {\n    btn() {},\n    async getBanner() {\n      const res = await getBanner();\n      this.banners = res.data;\n    }\n  }\n};", "map": {"version": 3, "names": ["getBanner", "data", "banners", "methods", "btn", "res"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\" ref=\"container\">\r\n   <ul>\r\n    <li v></li>\r\n   </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getBanner} from \"../api/banner.js\"\r\nexport default {\r\n  data(){\r\n    return{\r\n      banners:[]\r\n    }\r\n},\r\nmethods:{\r\n    btn(){\r\n     \r\n    },\r\n    async getBanner(){\r\n      const res = await getBanner()\r\n      this.banners = res.data\r\n    }\r\n}\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"less\">\r\n\r\n</style>"], "mappings": "AASA,SAAAA,SAAA;AACA;EACAC,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,GAEA;IACA,MAAAJ,UAAA;MACA,MAAAK,GAAA,SAAAL,SAAA;MACA,KAAAE,OAAA,GAAAG,GAAA,CAAAJ,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}