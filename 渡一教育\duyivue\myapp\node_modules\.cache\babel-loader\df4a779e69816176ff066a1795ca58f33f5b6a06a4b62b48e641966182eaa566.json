{"ast": null, "code": "import { getBanner } from \"../../api/banner.js\";\nexport default {\n  data() {\n    return {\n      banners: []\n    };\n  },\n  methods: {\n    async getBanner() {\n      const res = await getBanner();\n      this.banners = res.data;\n    }\n  }\n};", "map": {"version": 3, "names": ["getBanner", "data", "banners", "methods", "res"], "sources": ["src/views/Home/index.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"container\" v-if=\"banners.length>0\">\r\n    <ul>\r\n        <li v-for=\"item in banners\" :key=\"item.id\">\r\n            <img :src=\"item.img\" alt=\"\">\r\n            <h2>{{item.title}}</h2>\r\n        </li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getBanner} from \"../../api/banner.js\"\r\nexport default {\r\ndata( ){\r\n    return{\r\n      banners:[]\r\n    }\r\n},\r\nmethods:{\r\n    async getBanner(){\r\n      const res = await getBanner()\r\n      this.banners = res.data\r\n    }\r\n}\r\n}\r\n</script>\r\n\r\n<style>\r\n\r\n</style>"], "mappings": "AAYA,SAAAA,SAAA;AACA;EACAC,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,OAAA;IACA,MAAAH,UAAA;MACA,MAAAI,GAAA,SAAAJ,SAAA;MACA,KAAAE,OAAA,GAAAE,GAAA,CAAAH,IAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}