{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    ref: \"container\",\n    staticClass: \"home-container\"\n  }, [_c(\"ul\", [_c(\"ul\", _vm._l(_vm.banners, function (item) {\n    return _c(\"li\", {\n      key: item.id\n    }, [_c(\"img\", {\n      attrs: {\n        src: item.img,\n        alt: \"\"\n      }\n    }), _c(\"h2\", [_vm._v(_vm._s(item.title))])]);\n  }), 0)])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "_l", "banners", "item", "key", "id", "attrs", "src", "img", "alt", "_v", "_s", "title", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/views/Home.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"container\", staticClass: \"home-container\" }, [\n    _c(\"ul\", [\n      _c(\n        \"ul\",\n        _vm._l(_vm.banners, function (item) {\n          return _c(\"li\", { key: item.id }, [\n            _c(\"img\", { attrs: { src: item.img, alt: \"\" } }),\n            _c(\"h2\", [_vm._v(_vm._s(item.title))]),\n          ])\n        }),\n        0\n      ),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,GAAG,EAAE,WAAW;IAAEC,WAAW,EAAE;EAAiB,CAAC,EAAE,CACpEH,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CACA,IAAI,EACJD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,OAAO,EAAE,UAAUC,IAAI,EAAE;IAClC,OAAON,EAAE,CAAC,IAAI,EAAE;MAAEO,GAAG,EAAED,IAAI,CAACE;IAAG,CAAC,EAAE,CAChCR,EAAE,CAAC,KAAK,EAAE;MAAES,KAAK,EAAE;QAAEC,GAAG,EAAEJ,IAAI,CAACK,GAAG;QAAEC,GAAG,EAAE;MAAG;IAAE,CAAC,CAAC,EAChDZ,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACc,EAAE,CAACd,GAAG,CAACe,EAAE,CAACR,IAAI,CAACS,KAAK,CAAC,CAAC,CAAC,CAAC,CACvC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBlB,MAAM,CAACmB,aAAa,GAAG,IAAI;AAE3B,SAASnB,MAAM,EAAEkB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}