{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    attrs: {\n      id: \"app\"\n    }\n  }, [_c(\"h1\", [_vm._v(_vm._s(_vm.title))]), _c(\"p\", [_vm._v(_vm._s(_vm.message))]), _c(\"button\", {\n    on: {\n      click: _vm.changeMessage\n    }\n  }, [_vm._v(\"点击改变消息\")]), _c(\"div\", {\n    staticClass: \"test-component\"\n  }, [_c(\"h2\", [_vm._v(\"组件测试\")]), _c(\"ul\", _vm._l(_vm.items, function (item) {\n    return _c(\"li\", {\n      key: item.id\n    }, [_vm._v(\" \" + _vm._s(item.name) + \" \")]);\n  }), 0)])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "id", "_v", "_s", "title", "message", "on", "click", "changeMessage", "staticClass", "_l", "items", "item", "key", "name", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/App.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { attrs: { id: \"app\" } }, [\n    _c(\"h1\", [_vm._v(_vm._s(_vm.title))]),\n    _c(\"p\", [_vm._v(_vm._s(_vm.message))]),\n    _c(\"button\", { on: { click: _vm.changeMessage } }, [\n      _vm._v(\"点击改变消息\"),\n    ]),\n    _c(\"div\", { staticClass: \"test-component\" }, [\n      _c(\"h2\", [_vm._v(\"组件测试\")]),\n      _c(\n        \"ul\",\n        _vm._l(_vm.items, function (item) {\n          return _c(\"li\", { key: item.id }, [\n            _vm._v(\" \" + _vm._s(item.name) + \" \"),\n          ])\n        }),\n        0\n      ),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE;IAAEE,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAM;EAAE,CAAC,EAAE,CACzCH,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,KAAK,CAAC,CAAC,CAAC,CAAC,EACrCN,EAAE,CAAC,GAAG,EAAE,CAACD,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACQ,OAAO,CAAC,CAAC,CAAC,CAAC,EACtCP,EAAE,CAAC,QAAQ,EAAE;IAAEQ,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAAC<PERSON>;IAAc;EAAE,CAAC,EAAE,CACjDX,GAAG,CAACK,EAAE,CAAC,QAAQ,CAAC,CACjB,CAAC,EACFJ,EAAE,CAAC,KAAK,EAAE;IAAEW,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CX,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC1BJ,EAAE,CACA,IAAI,EACJD,GAAG,CAACa,EAAE,CAACb,GAAG,CAACc,KAAK,EAAE,UAAUC,IAAI,EAAE;IAChC,OAAOd,EAAE,CAAC,IAAI,EAAE;MAAEe,GAAG,EAAED,IAAI,CAACX;IAAG,CAAC,EAAE,CAChCJ,GAAG,CAACK,EAAE,CAAC,GAAG,GAAGL,GAAG,CAACM,EAAE,CAACS,IAAI,CAACE,IAAI,CAAC,GAAG,GAAG,CAAC,CACtC,CAAC;EACJ,CAAC,CAAC,EACF,CACF,CAAC,CACF,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBnB,MAAM,CAACoB,aAAa,GAAG,IAAI;AAE3B,SAASpB,MAAM,EAAEmB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}