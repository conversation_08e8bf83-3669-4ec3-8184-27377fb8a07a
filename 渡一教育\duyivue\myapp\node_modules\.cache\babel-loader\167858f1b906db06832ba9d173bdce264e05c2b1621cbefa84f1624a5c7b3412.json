{"ast": null, "code": "import MyPager from \"/MyIndex.vue\";\nexport default {\n  setup() {},\n  components: {\n    MyPager\n  },\n  data() {\n    return {\n      url: \"/img/vatar.jpg\",\n      curren: 2\n    };\n  },\n  methods: {\n    handelchange(newpage) {\n      if (newpage < 1) {\n        newpage = 1;\n      }\n      if (newpage > this.pageNumber) {\n        newpage = this.pageNumber;\n      }\n      if (newpage === this.curren) {\n        return;\n      }\n      console.log(\"改变了\", newpage);\n      this.curren = newpage;\n    }\n  }\n};", "map": {"version": 3, "names": ["MyPager", "setup", "components", "data", "url", "curren", "methods", "handelchange", "newpage", "pageNumber", "console", "log"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n<!-- \n父组件可以影响子组件的根元素，\n1.在style中导入阿里巴巴字体图标库\n2.然后根据他提供的数据来写组件\n-->\n  <div>\n    <MyPager :curren=\"curren\" :total=\"300\" :limit=\"10\" @handelchange=\"handelchange\"></MyPager>\n  </div>\n</template>\n<script>\nimport MyPager from \"/MyIndex.vue\";\nexport default {\n  setup() {\n    \n  },\n  components:{\n MyPager,\n\n  },\n  data(){\n    return{\n      url:\"/img/vatar.jpg\",\n      curren:2,\n    }\n  },\n  methods:{\n    handelchange(newpage){\n      if(newpage<1){\n        newpage=1\n      }\n      if(newpage>this.pageNumber){\n        newpage=this.pageNumber\n      }\n      if(newpage===this.curren){\n        return\n      }\n    console.log(\"改变了\",newpage);\n    this.curren=newpage\n    \n  }\n  }\n}\n</script>"], "mappings": "AAWA,OAAAA,OAAA;AACA;EACAC,MAAA,GAEA;EACAC,UAAA;IACAF;EAEA;EACAG,KAAA;IACA;MACAC,GAAA;MACAC,MAAA;IACA;EACA;EACAC,OAAA;IACAC,aAAAC,OAAA;MACA,IAAAA,OAAA;QACAA,OAAA;MACA;MACA,IAAAA,OAAA,QAAAC,UAAA;QACAD,OAAA,QAAAC,UAAA;MACA;MACA,IAAAD,OAAA,UAAAH,MAAA;QACA;MACA;MACAK,OAAA,CAAAC,GAAA,QAAAH,OAAA;MACA,KAAAH,MAAA,GAAAG,OAAA;IAEA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}