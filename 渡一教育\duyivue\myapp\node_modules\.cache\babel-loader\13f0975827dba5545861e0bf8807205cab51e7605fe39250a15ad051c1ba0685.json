{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\nimport App from '../App.vue';\nVue.use(VueRouter);\nconst routes = [{\n  path: '/',\n  name: 'home',\n  component: App\n}, {\n  path: '/about',\n  name: 'about',\n  // route level code-splitting\n  // this generates a separate chunk (about.[hash].js) for this route\n  // which is lazy-loaded when the route is visited.\n  component: () => import(/* webpackChunkName: \"about\" */'../views/AboutView.vue')\n}];\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "App", "use", "routes", "path", "name", "component", "router", "mode", "base", "process", "env", "BASE_URL"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport App from '../App.vue'\n\nVue.use(VueRouter)\n\nconst routes = [\n  {\n    path: '/',\n    name: 'home',\n    component: App\n  },\n  {\n    path: '/about',\n    name: 'about',\n    // route level code-splitting\n    // this generates a separate chunk (about.[hash].js) for this route\n    // which is lazy-loaded when the route is visited.\n    component: () => import(/* webpackChunkName: \"about\" */ '../views/AboutView.vue')\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\nexport default router\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,GAAG,MAAM,YAAY;AAE5BF,GAAG,CAACG,GAAG,CAACF,SAAS,CAAC;AAElB,MAAMG,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEL;AACb,CAAC,EACD;EACEG,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACb;EACA;EACA;EACAC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAAgC,wBAAwB;AAClF,CAAC,CACF;AAED,MAAMC,MAAM,GAAG,IAAIP,SAAS,CAAC;EAC3BQ,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BT;AACF,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}