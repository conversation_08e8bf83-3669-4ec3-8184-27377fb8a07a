{"ast": null, "code": "import { getBanner } from \"../../api/banner.js\";\nexport default {\n  data() {\n    return {\n      banners: []\n      // container:null,\n    };\n  },\n  async created() {\n    this.banners = await getBanner();\n  }\n};", "map": {"version": 3, "names": ["getBanner", "data", "banners", "created"], "sources": ["src/views/Home/index.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"container\" v-if=\"banners.length>0\"> \r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getBanner} from \"../../api/banner.js\"\r\nexport default {\r\ndata() {\r\n  return {\r\n    banners:[],\r\n    // container:null,\r\n  }\r\n},\r\nasync created(){\r\n    this.banners = await getBanner()\r\n}\r\n}\r\n</script>\r\n\r\n<style>\r\n\r\n</style>"], "mappings": "AAOA,SAAAA,SAAA;AACA;EACAC,KAAA;IACA;MACAC,OAAA;MACA;IACA;EACA;EACA,MAAAC,QAAA;IACA,KAAAD,OAAA,SAAAF,SAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}