<template>
<!-- 头像组件
传siez属性进来控制大小
相关知识父子间通信
 -->

    <img :src="url" alt="" :style="{
        width:size + 'px',
        height:size + 'px',
        borderRadius:radius
    }" class="avatar-container">

</template>

<script>

export default {
    props:{
        url:String,
        size:{
            type:Number,
            default:100
        },
       
        
    },
    data(){
        return{

        }
    }

}
</script>

<style scoped>
.avatar-container{
    /* 防止图片拉伸： object-fit: cover;*/
    object-fit: cover;
    border-radius: 50%;
}

</style>