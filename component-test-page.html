<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue组件测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.16/dist/vue.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .component-wrapper {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>🧪 Vue组件独立测试环境</h1>
                <p>在这里可以单独测试各种Vue组件功能</p>
            </div>
            
            <div class="component-wrapper">
                <test-component></test-component>
            </div>
        </div>
    </div>

    <script>
        // 定义测试组件
        Vue.component('test-component', {
            template: `
                <div class="test-component">
                    <h2>{{ title }}</h2>
                    
                    <!-- 基础数据绑定测试 -->
                    <section class="test-section">
                        <h3>1. 数据绑定测试</h3>
                        <p>消息: {{ message }}</p>
                        <button @click="updateMessage">更新消息</button>
                    </section>

                    <!-- 列表渲染测试 -->
                    <section class="test-section">
                        <h3>2. 列表渲染测试</h3>
                        <ul>
                            <li v-for="item in items" :key="item.id" class="list-item">
                                {{ item.name }} 
                                <button @click="removeItem(item.id)" class="remove-btn">删除</button>
                            </li>
                        </ul>
                        <div class="add-item">
                            <input v-model="newItemName" placeholder="输入新项目名称" @keyup.enter="addItem">
                            <button @click="addItem">添加项目</button>
                        </div>
                    </section>

                    <!-- 表单输入测试 -->
                    <section class="test-section">
                        <h3>3. 表单输入测试</h3>
                        <div class="form-group">
                            <label>姓名:</label>
                            <input v-model="form.name" placeholder="请输入姓名">
                        </div>
                        <div class="form-group">
                            <label>年龄:</label>
                            <input v-model.number="form.age" type="number" placeholder="请输入年龄">
                        </div>
                        <div class="form-group">
                            <label>性别:</label>
                            <select v-model="form.gender">
                                <option value="">请选择</option>
                                <option value="male">男</option>
                                <option value="female">女</option>
                            </select>
                        </div>
                        <button @click="submitForm" :disabled="!isFormValid">提交表单</button>
                        <div v-if="formResult" class="form-result">
                            <h4>表单结果:</h4>
                            <pre>{{ formResult }}</pre>
                        </div>
                    </section>

                    <!-- 条件渲染测试 -->
                    <section class="test-section">
                        <h3>4. 条件渲染测试</h3>
                        <button @click="toggleContent">{{ showContent ? '隐藏' : '显示' }}内容</button>
                        <div v-if="showContent" class="conditional-content">
                            <p>这是条件渲染的内容</p>
                            <p>当前时间: {{ currentTime }}</p>
                        </div>
                    </section>

                    <!-- 计算属性测试 -->
                    <section class="test-section">
                        <h3>5. 计算属性测试</h3>
                        <p>项目总数: {{ itemCount }}</p>
                        <p>项目名称: {{ itemNames }}</p>
                        <p>表单完整度: {{ formCompleteness }}%</p>
                    </section>
                </div>
            `,
            data() {
                return {
                    title: 'Vue组件功能测试',
                    message: '初始消息',
                    newItemName: '',
                    showContent: true,
                    formResult: null,
                    currentTime: new Date().toLocaleString(),
                    items: [
                        { id: 1, name: '测试项目1' },
                        { id: 2, name: '测试项目2' },
                        { id: 3, name: '测试项目3' }
                    ],
                    form: {
                        name: '',
                        age: null,
                        gender: ''
                    }
                }
            },
            computed: {
                itemCount() {
                    return this.items.length;
                },
                itemNames() {
                    return this.items.map(item => item.name).join(', ');
                },
                formCompleteness() {
                    const fields = ['name', 'age', 'gender'];
                    const completed = fields.filter(field => this.form[field]).length;
                    return Math.round((completed / fields.length) * 100);
                },
                isFormValid() {
                    return this.form.name && this.form.age && this.form.gender;
                }
            },
            methods: {
                updateMessage() {
                    this.message = \`消息已更新 - \${new Date().toLocaleTimeString()}\`;
                },
                addItem() {
                    if (this.newItemName.trim()) {
                        this.items.push({
                            id: Date.now(),
                            name: this.newItemName.trim()
                        });
                        this.newItemName = '';
                    }
                },
                removeItem(id) {
                    this.items = this.items.filter(item => item.id !== id);
                },
                toggleContent() {
                    this.showContent = !this.showContent;
                    if (this.showContent) {
                        this.currentTime = new Date().toLocaleString();
                    }
                },
                submitForm() {
                    this.formResult = JSON.stringify(this.form, null, 2);
                }
            },
            mounted() {
                console.log('TestComponent 已挂载');
                // 每秒更新时间
                setInterval(() => {
                    if (this.showContent) {
                        this.currentTime = new Date().toLocaleString();
                    }
                }, 1000);
            }
        });

        // 创建Vue实例
        new Vue({
            el: '#app'
        });
    </script>

    <style>
        .test-component {
            padding: 20px;
        }

        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .test-section h3 {
            color: #42b983;
            margin-top: 0;
        }

        .list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-radius: 4px;
        }

        .remove-btn {
            background: #ff4757;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .add-item {
            margin-top: 10px;
        }

        .add-item input {
            padding: 8px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }

        .form-group {
            margin: 10px 0;
        }

        .form-group label {
            display: inline-block;
            width: 80px;
            font-weight: bold;
        }

        .form-group input, .form-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-left: 10px;
        }

        .form-result {
            margin-top: 15px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 4px;
        }

        .conditional-content {
            margin-top: 10px;
            padding: 15px;
            background: #fff3cd;
            border-radius: 4px;
        }

        button {
            background: #42b983;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }

        button:hover {
            background: #369870;
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</body>
</html>
