<template>
  <div class="home-container" ref="container">
   <ul>
     <ul>
        <li v-for="item in banners" :key="item.id">
            <img :src="item.img" alt="">
            <h2>{{item.title}}</h2>
        </li>
    </ul>
   </ul>
  </div>
</template>

<script>
import {getBanner} from "../api/banner.js"
export default {
  data(){
    return{
      banners:[]
    };
},
async created(){
this.banners = await getBanner()
},

}
</script>

<style scoped lang="less">

</style>