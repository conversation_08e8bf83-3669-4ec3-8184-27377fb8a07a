{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\n/*!\n  * vue-router v3.6.5\n  * (c) 2022 Evan You\n  * @license MIT\n  */\n/*  */\n\nfunction assert(condition, message) {\n  if (!condition) {\n    throw new Error(\"[vue-router] \" + message);\n  }\n}\nfunction warn(condition, message) {\n  if (!condition) {\n    typeof console !== 'undefined' && console.warn(\"[vue-router] \" + message);\n  }\n}\nfunction extend(a, b) {\n  for (var key in b) {\n    a[key] = b[key];\n  }\n  return a;\n}\n\n/*  */\n\nvar encodeReserveRE = /[!'()*]/g;\nvar encodeReserveReplacer = function (c) {\n  return '%' + c.charCodeAt(0).toString(16);\n};\nvar commaRE = /%2C/g;\n\n// fixed encodeURIComponent which is more conformant to RFC3986:\n// - escapes [!'()*]\n// - preserve commas\nvar encode = function (str) {\n  return encodeURIComponent(str).replace(encodeReserveRE, encodeReserveReplacer).replace(commaRE, ',');\n};\nfunction decode(str) {\n  try {\n    return decodeURIComponent(str);\n  } catch (err) {\n    if (process.env.NODE_ENV !== 'production') {\n      warn(false, \"Error decoding \\\"\" + str + \"\\\". Leaving it intact.\");\n    }\n  }\n  return str;\n}\nfunction resolveQuery(query, extraQuery, _parseQuery) {\n  if (extraQuery === void 0) extraQuery = {};\n  var parse = _parseQuery || parseQuery;\n  var parsedQuery;\n  try {\n    parsedQuery = parse(query || '');\n  } catch (e) {\n    process.env.NODE_ENV !== 'production' && warn(false, e.message);\n    parsedQuery = {};\n  }\n  for (var key in extraQuery) {\n    var value = extraQuery[key];\n    parsedQuery[key] = Array.isArray(value) ? value.map(castQueryParamValue) : castQueryParamValue(value);\n  }\n  return parsedQuery;\n}\nvar castQueryParamValue = function (value) {\n  return value == null || typeof value === 'object' ? value : String(value);\n};\nfunction parseQuery(query) {\n  var res = {};\n  query = query.trim().replace(/^(\\?|#|&)/, '');\n  if (!query) {\n    return res;\n  }\n  query.split('&').forEach(function (param) {\n    var parts = param.replace(/\\+/g, ' ').split('=');\n    var key = decode(parts.shift());\n    var val = parts.length > 0 ? decode(parts.join('=')) : null;\n    if (res[key] === undefined) {\n      res[key] = val;\n    } else if (Array.isArray(res[key])) {\n      res[key].push(val);\n    } else {\n      res[key] = [res[key], val];\n    }\n  });\n  return res;\n}\nfunction stringifyQuery(obj) {\n  var res = obj ? Object.keys(obj).map(function (key) {\n    var val = obj[key];\n    if (val === undefined) {\n      return '';\n    }\n    if (val === null) {\n      return encode(key);\n    }\n    if (Array.isArray(val)) {\n      var result = [];\n      val.forEach(function (val2) {\n        if (val2 === undefined) {\n          return;\n        }\n        if (val2 === null) {\n          result.push(encode(key));\n        } else {\n          result.push(encode(key) + '=' + encode(val2));\n        }\n      });\n      return result.join('&');\n    }\n    return encode(key) + '=' + encode(val);\n  }).filter(function (x) {\n    return x.length > 0;\n  }).join('&') : null;\n  return res ? \"?\" + res : '';\n}\n\n/*  */\n\nvar trailingSlashRE = /\\/?$/;\nfunction createRoute(record, location, redirectedFrom, router) {\n  var stringifyQuery = router && router.options.stringifyQuery;\n  var query = location.query || {};\n  try {\n    query = clone(query);\n  } catch (e) {}\n  var route = {\n    name: location.name || record && record.name,\n    meta: record && record.meta || {},\n    path: location.path || '/',\n    hash: location.hash || '',\n    query: query,\n    params: location.params || {},\n    fullPath: getFullPath(location, stringifyQuery),\n    matched: record ? formatMatch(record) : []\n  };\n  if (redirectedFrom) {\n    route.redirectedFrom = getFullPath(redirectedFrom, stringifyQuery);\n  }\n  return Object.freeze(route);\n}\nfunction clone(value) {\n  if (Array.isArray(value)) {\n    return value.map(clone);\n  } else if (value && typeof value === 'object') {\n    var res = {};\n    for (var key in value) {\n      res[key] = clone(value[key]);\n    }\n    return res;\n  } else {\n    return value;\n  }\n}\n\n// the starting route that represents the initial state\nvar START = createRoute(null, {\n  path: '/'\n});\nfunction formatMatch(record) {\n  var res = [];\n  while (record) {\n    res.unshift(record);\n    record = record.parent;\n  }\n  return res;\n}\nfunction getFullPath(ref, _stringifyQuery) {\n  var path = ref.path;\n  var query = ref.query;\n  if (query === void 0) query = {};\n  var hash = ref.hash;\n  if (hash === void 0) hash = '';\n  var stringify = _stringifyQuery || stringifyQuery;\n  return (path || '/') + stringify(query) + hash;\n}\nfunction isSameRoute(a, b, onlyPath) {\n  if (b === START) {\n    return a === b;\n  } else if (!b) {\n    return false;\n  } else if (a.path && b.path) {\n    return a.path.replace(trailingSlashRE, '') === b.path.replace(trailingSlashRE, '') && (onlyPath || a.hash === b.hash && isObjectEqual(a.query, b.query));\n  } else if (a.name && b.name) {\n    return a.name === b.name && (onlyPath || a.hash === b.hash && isObjectEqual(a.query, b.query) && isObjectEqual(a.params, b.params));\n  } else {\n    return false;\n  }\n}\nfunction isObjectEqual(a, b) {\n  if (a === void 0) a = {};\n  if (b === void 0) b = {};\n\n  // handle null value #1566\n  if (!a || !b) {\n    return a === b;\n  }\n  var aKeys = Object.keys(a).sort();\n  var bKeys = Object.keys(b).sort();\n  if (aKeys.length !== bKeys.length) {\n    return false;\n  }\n  return aKeys.every(function (key, i) {\n    var aVal = a[key];\n    var bKey = bKeys[i];\n    if (bKey !== key) {\n      return false;\n    }\n    var bVal = b[key];\n    // query values can be null and undefined\n    if (aVal == null || bVal == null) {\n      return aVal === bVal;\n    }\n    // check nested equality\n    if (typeof aVal === 'object' && typeof bVal === 'object') {\n      return isObjectEqual(aVal, bVal);\n    }\n    return String(aVal) === String(bVal);\n  });\n}\nfunction isIncludedRoute(current, target) {\n  return current.path.replace(trailingSlashRE, '/').indexOf(target.path.replace(trailingSlashRE, '/')) === 0 && (!target.hash || current.hash === target.hash) && queryIncludes(current.query, target.query);\n}\nfunction queryIncludes(current, target) {\n  for (var key in target) {\n    if (!(key in current)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction handleRouteEntered(route) {\n  for (var i = 0; i < route.matched.length; i++) {\n    var record = route.matched[i];\n    for (var name in record.instances) {\n      var instance = record.instances[name];\n      var cbs = record.enteredCbs[name];\n      if (!instance || !cbs) {\n        continue;\n      }\n      delete record.enteredCbs[name];\n      for (var i$1 = 0; i$1 < cbs.length; i$1++) {\n        if (!instance._isBeingDestroyed) {\n          cbs[i$1](instance);\n        }\n      }\n    }\n  }\n}\nvar View = {\n  name: 'RouterView',\n  functional: true,\n  props: {\n    name: {\n      type: String,\n      default: 'default'\n    }\n  },\n  render: function render(_, ref) {\n    var props = ref.props;\n    var children = ref.children;\n    var parent = ref.parent;\n    var data = ref.data;\n\n    // used by devtools to display a router-view badge\n    data.routerView = true;\n\n    // directly use parent context's createElement() function\n    // so that components rendered by router-view can resolve named slots\n    var h = parent.$createElement;\n    var name = props.name;\n    var route = parent.$route;\n    var cache = parent._routerViewCache || (parent._routerViewCache = {});\n\n    // determine current view depth, also check to see if the tree\n    // has been toggled inactive but kept-alive.\n    var depth = 0;\n    var inactive = false;\n    while (parent && parent._routerRoot !== parent) {\n      var vnodeData = parent.$vnode ? parent.$vnode.data : {};\n      if (vnodeData.routerView) {\n        depth++;\n      }\n      if (vnodeData.keepAlive && parent._directInactive && parent._inactive) {\n        inactive = true;\n      }\n      parent = parent.$parent;\n    }\n    data.routerViewDepth = depth;\n\n    // render previous view if the tree is inactive and kept-alive\n    if (inactive) {\n      var cachedData = cache[name];\n      var cachedComponent = cachedData && cachedData.component;\n      if (cachedComponent) {\n        // #2301\n        // pass props\n        if (cachedData.configProps) {\n          fillPropsinData(cachedComponent, data, cachedData.route, cachedData.configProps);\n        }\n        return h(cachedComponent, data, children);\n      } else {\n        // render previous empty view\n        return h();\n      }\n    }\n    var matched = route.matched[depth];\n    var component = matched && matched.components[name];\n\n    // render empty node if no matched route or no config component\n    if (!matched || !component) {\n      cache[name] = null;\n      return h();\n    }\n\n    // cache component\n    cache[name] = {\n      component: component\n    };\n\n    // attach instance registration hook\n    // this will be called in the instance's injected lifecycle hooks\n    data.registerRouteInstance = function (vm, val) {\n      // val could be undefined for unregistration\n      var current = matched.instances[name];\n      if (val && current !== vm || !val && current === vm) {\n        matched.instances[name] = val;\n      }\n    }\n\n    // also register instance in prepatch hook\n    // in case the same component instance is reused across different routes\n    ;\n    (data.hook || (data.hook = {})).prepatch = function (_, vnode) {\n      matched.instances[name] = vnode.componentInstance;\n    };\n\n    // register instance in init hook\n    // in case kept-alive component be actived when routes changed\n    data.hook.init = function (vnode) {\n      if (vnode.data.keepAlive && vnode.componentInstance && vnode.componentInstance !== matched.instances[name]) {\n        matched.instances[name] = vnode.componentInstance;\n      }\n\n      // if the route transition has already been confirmed then we weren't\n      // able to call the cbs during confirmation as the component was not\n      // registered yet, so we call it here.\n      handleRouteEntered(route);\n    };\n    var configProps = matched.props && matched.props[name];\n    // save route and configProps in cache\n    if (configProps) {\n      extend(cache[name], {\n        route: route,\n        configProps: configProps\n      });\n      fillPropsinData(component, data, route, configProps);\n    }\n    return h(component, data, children);\n  }\n};\nfunction fillPropsinData(component, data, route, configProps) {\n  // resolve props\n  var propsToPass = data.props = resolveProps(route, configProps);\n  if (propsToPass) {\n    // clone to prevent mutation\n    propsToPass = data.props = extend({}, propsToPass);\n    // pass non-declared props as attrs\n    var attrs = data.attrs = data.attrs || {};\n    for (var key in propsToPass) {\n      if (!component.props || !(key in component.props)) {\n        attrs[key] = propsToPass[key];\n        delete propsToPass[key];\n      }\n    }\n  }\n}\nfunction resolveProps(route, config) {\n  switch (typeof config) {\n    case 'undefined':\n      return;\n    case 'object':\n      return config;\n    case 'function':\n      return config(route);\n    case 'boolean':\n      return config ? route.params : undefined;\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        warn(false, \"props in \\\"\" + route.path + \"\\\" is a \" + typeof config + \", \" + \"expecting an object, function or boolean.\");\n      }\n  }\n}\n\n/*  */\n\nfunction resolvePath(relative, base, append) {\n  var firstChar = relative.charAt(0);\n  if (firstChar === '/') {\n    return relative;\n  }\n  if (firstChar === '?' || firstChar === '#') {\n    return base + relative;\n  }\n  var stack = base.split('/');\n\n  // remove trailing segment if:\n  // - not appending\n  // - appending to trailing slash (last segment is empty)\n  if (!append || !stack[stack.length - 1]) {\n    stack.pop();\n  }\n\n  // resolve relative path\n  var segments = relative.replace(/^\\//, '').split('/');\n  for (var i = 0; i < segments.length; i++) {\n    var segment = segments[i];\n    if (segment === '..') {\n      stack.pop();\n    } else if (segment !== '.') {\n      stack.push(segment);\n    }\n  }\n\n  // ensure leading slash\n  if (stack[0] !== '') {\n    stack.unshift('');\n  }\n  return stack.join('/');\n}\nfunction parsePath(path) {\n  var hash = '';\n  var query = '';\n  var hashIndex = path.indexOf('#');\n  if (hashIndex >= 0) {\n    hash = path.slice(hashIndex);\n    path = path.slice(0, hashIndex);\n  }\n  var queryIndex = path.indexOf('?');\n  if (queryIndex >= 0) {\n    query = path.slice(queryIndex + 1);\n    path = path.slice(0, queryIndex);\n  }\n  return {\n    path: path,\n    query: query,\n    hash: hash\n  };\n}\nfunction cleanPath(path) {\n  return path.replace(/\\/(?:\\s*\\/)+/g, '/');\n}\nvar isarray = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};\n\n/**\n * Expose `pathToRegexp`.\n */\nvar pathToRegexp_1 = pathToRegexp;\nvar parse_1 = parse;\nvar compile_1 = compile;\nvar tokensToFunction_1 = tokensToFunction;\nvar tokensToRegExp_1 = tokensToRegExp;\n\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\nvar PATH_REGEXP = new RegExp([\n// Match escaped characters that would otherwise appear in future matches.\n// This allows the user to escape special characters that won't transform.\n'(\\\\\\\\.)',\n// Match Express-style parameters and un-named parameters with a prefix\n// and optional suffixes. Matches appear as:\n//\n// \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n// \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n// \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n'([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?|(\\\\*))'].join('|'), 'g');\n\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {string}  str\n * @param  {Object=} options\n * @return {!Array}\n */\nfunction parse(str, options) {\n  var tokens = [];\n  var key = 0;\n  var index = 0;\n  var path = '';\n  var defaultDelimiter = options && options.delimiter || '/';\n  var res;\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0];\n    var escaped = res[1];\n    var offset = res.index;\n    path += str.slice(index, offset);\n    index = offset + m.length;\n\n    // Ignore already escaped sequences.\n    if (escaped) {\n      path += escaped[1];\n      continue;\n    }\n    var next = str[index];\n    var prefix = res[2];\n    var name = res[3];\n    var capture = res[4];\n    var group = res[5];\n    var modifier = res[6];\n    var asterisk = res[7];\n\n    // Push the current path onto the tokens.\n    if (path) {\n      tokens.push(path);\n      path = '';\n    }\n    var partial = prefix != null && next != null && next !== prefix;\n    var repeat = modifier === '+' || modifier === '*';\n    var optional = modifier === '?' || modifier === '*';\n    var delimiter = res[2] || defaultDelimiter;\n    var pattern = capture || group;\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      partial: partial,\n      asterisk: !!asterisk,\n      pattern: pattern ? escapeGroup(pattern) : asterisk ? '.*' : '[^' + escapeString(delimiter) + ']+?'\n    });\n  }\n\n  // Match any characters still remaining.\n  if (index < str.length) {\n    path += str.substr(index);\n  }\n\n  // If the path exists, push it onto the end.\n  if (path) {\n    tokens.push(path);\n  }\n  return tokens;\n}\n\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {string}             str\n * @param  {Object=}            options\n * @return {!function(Object=, Object=)}\n */\nfunction compile(str, options) {\n  return tokensToFunction(parse(str, options), options);\n}\n\n/**\n * Prettier encoding of URI path segments.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeURIComponentPretty(str) {\n  return encodeURI(str).replace(/[\\/?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\n\n/**\n * Encode the asterisk parameter. Similar to `pretty`, but allows slashes.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeAsterisk(str) {\n  return encodeURI(str).replace(/[?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nfunction tokensToFunction(tokens, options) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length);\n\n  // Compile all the patterns before compilation.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$', flags(options));\n    }\n  }\n  return function (obj, opts) {\n    var path = '';\n    var data = obj || {};\n    var options = opts || {};\n    var encode = options.pretty ? encodeURIComponentPretty : encodeURIComponent;\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n      if (typeof token === 'string') {\n        path += token;\n        continue;\n      }\n      var value = data[token.name];\n      var segment;\n      if (value == null) {\n        if (token.optional) {\n          // Prepend partial segment prefixes.\n          if (token.partial) {\n            path += token.prefix;\n          }\n          continue;\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined');\n        }\n      }\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received `' + JSON.stringify(value) + '`');\n        }\n        if (value.length === 0) {\n          if (token.optional) {\n            continue;\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty');\n          }\n        }\n        for (var j = 0; j < value.length; j++) {\n          segment = encode(value[j]);\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received `' + JSON.stringify(segment) + '`');\n          }\n          path += (j === 0 ? token.prefix : token.delimiter) + segment;\n        }\n        continue;\n      }\n      segment = token.asterisk ? encodeAsterisk(value) : encode(value);\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"');\n      }\n      path += token.prefix + segment;\n    }\n    return path;\n  };\n}\n\n/**\n * Escape a regular expression string.\n *\n * @param  {string} str\n * @return {string}\n */\nfunction escapeString(str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/\\\\])/g, '\\\\$1');\n}\n\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {string} group\n * @return {string}\n */\nfunction escapeGroup(group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1');\n}\n\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {!RegExp} re\n * @param  {Array}   keys\n * @return {!RegExp}\n */\nfunction attachKeys(re, keys) {\n  re.keys = keys;\n  return re;\n}\n\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {string}\n */\nfunction flags(options) {\n  return options && options.sensitive ? '' : 'i';\n}\n\n/**\n * Pull out keys from a regexp.\n *\n * @param  {!RegExp} path\n * @param  {!Array}  keys\n * @return {!RegExp}\n */\nfunction regexpToRegexp(path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g);\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        partial: false,\n        asterisk: false,\n        pattern: null\n      });\n    }\n  }\n  return attachKeys(path, keys);\n}\n\n/**\n * Transform an array into a regexp.\n *\n * @param  {!Array}  path\n * @param  {Array}   keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction arrayToRegexp(path, keys, options) {\n  var parts = [];\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source);\n  }\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options));\n  return attachKeys(regexp, keys);\n}\n\n/**\n * Create a path regexp from string input.\n *\n * @param  {string}  path\n * @param  {!Array}  keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction stringToRegexp(path, keys, options) {\n  return tokensToRegExp(parse(path, options), keys, options);\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {!Array}          tokens\n * @param  {(Array|Object)=} keys\n * @param  {Object=}         options\n * @return {!RegExp}\n */\nfunction tokensToRegExp(tokens, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */keys || options;\n    keys = [];\n  }\n  options = options || {};\n  var strict = options.strict;\n  var end = options.end !== false;\n  var route = '';\n\n  // Iterate over the tokens and create our regexp string.\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i];\n    if (typeof token === 'string') {\n      route += escapeString(token);\n    } else {\n      var prefix = escapeString(token.prefix);\n      var capture = '(?:' + token.pattern + ')';\n      keys.push(token);\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*';\n      }\n      if (token.optional) {\n        if (!token.partial) {\n          capture = '(?:' + prefix + '(' + capture + '))?';\n        } else {\n          capture = prefix + '(' + capture + ')?';\n        }\n      } else {\n        capture = prefix + '(' + capture + ')';\n      }\n      route += capture;\n    }\n  }\n  var delimiter = escapeString(options.delimiter || '/');\n  var endsWithDelimiter = route.slice(-delimiter.length) === delimiter;\n\n  // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n  if (!strict) {\n    route = (endsWithDelimiter ? route.slice(0, -delimiter.length) : route) + '(?:' + delimiter + '(?=$))?';\n  }\n  if (end) {\n    route += '$';\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithDelimiter ? '' : '(?=' + delimiter + '|$)';\n  }\n  return attachKeys(new RegExp('^' + route, flags(options)), keys);\n}\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(string|RegExp|Array)} path\n * @param  {(Array|Object)=}       keys\n * @param  {Object=}               options\n * @return {!RegExp}\n */\nfunction pathToRegexp(path, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */keys || options;\n    keys = [];\n  }\n  options = options || {};\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path, /** @type {!Array} */keys);\n  }\n  if (isarray(path)) {\n    return arrayToRegexp(/** @type {!Array} */path, /** @type {!Array} */keys, options);\n  }\n  return stringToRegexp(/** @type {string} */path, /** @type {!Array} */keys, options);\n}\npathToRegexp_1.parse = parse_1;\npathToRegexp_1.compile = compile_1;\npathToRegexp_1.tokensToFunction = tokensToFunction_1;\npathToRegexp_1.tokensToRegExp = tokensToRegExp_1;\n\n/*  */\n\n// $flow-disable-line\nvar regexpCompileCache = Object.create(null);\nfunction fillParams(path, params, routeMsg) {\n  params = params || {};\n  try {\n    var filler = regexpCompileCache[path] || (regexpCompileCache[path] = pathToRegexp_1.compile(path));\n\n    // Fix #2505 resolving asterisk routes { name: 'not-found', params: { pathMatch: '/not-found' }}\n    // and fix #3106 so that you can work with location descriptor object having params.pathMatch equal to empty string\n    if (typeof params.pathMatch === 'string') {\n      params[0] = params.pathMatch;\n    }\n    return filler(params, {\n      pretty: true\n    });\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      // Fix #3072 no warn if `pathMatch` is string\n      warn(typeof params.pathMatch === 'string', \"missing param for \" + routeMsg + \": \" + e.message);\n    }\n    return '';\n  } finally {\n    // delete the 0 if it was added\n    delete params[0];\n  }\n}\n\n/*  */\n\nfunction normalizeLocation(raw, current, append, router) {\n  var next = typeof raw === 'string' ? {\n    path: raw\n  } : raw;\n  // named target\n  if (next._normalized) {\n    return next;\n  } else if (next.name) {\n    next = extend({}, raw);\n    var params = next.params;\n    if (params && typeof params === 'object') {\n      next.params = extend({}, params);\n    }\n    return next;\n  }\n\n  // relative params\n  if (!next.path && next.params && current) {\n    next = extend({}, next);\n    next._normalized = true;\n    var params$1 = extend(extend({}, current.params), next.params);\n    if (current.name) {\n      next.name = current.name;\n      next.params = params$1;\n    } else if (current.matched.length) {\n      var rawPath = current.matched[current.matched.length - 1].path;\n      next.path = fillParams(rawPath, params$1, \"path \" + current.path);\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn(false, \"relative params navigation requires a current route.\");\n    }\n    return next;\n  }\n  var parsedPath = parsePath(next.path || '');\n  var basePath = current && current.path || '/';\n  var path = parsedPath.path ? resolvePath(parsedPath.path, basePath, append || next.append) : basePath;\n  var query = resolveQuery(parsedPath.query, next.query, router && router.options.parseQuery);\n  var hash = next.hash || parsedPath.hash;\n  if (hash && hash.charAt(0) !== '#') {\n    hash = \"#\" + hash;\n  }\n  return {\n    _normalized: true,\n    path: path,\n    query: query,\n    hash: hash\n  };\n}\n\n/*  */\n\n// work around weird flow bug\nvar toTypes = [String, Object];\nvar eventTypes = [String, Array];\nvar noop = function () {};\nvar warnedCustomSlot;\nvar warnedTagProp;\nvar warnedEventProp;\nvar Link = {\n  name: 'RouterLink',\n  props: {\n    to: {\n      type: toTypes,\n      required: true\n    },\n    tag: {\n      type: String,\n      default: 'a'\n    },\n    custom: Boolean,\n    exact: Boolean,\n    exactPath: Boolean,\n    append: Boolean,\n    replace: Boolean,\n    activeClass: String,\n    exactActiveClass: String,\n    ariaCurrentValue: {\n      type: String,\n      default: 'page'\n    },\n    event: {\n      type: eventTypes,\n      default: 'click'\n    }\n  },\n  render: function render(h) {\n    var this$1$1 = this;\n    var router = this.$router;\n    var current = this.$route;\n    var ref = router.resolve(this.to, current, this.append);\n    var location = ref.location;\n    var route = ref.route;\n    var href = ref.href;\n    var classes = {};\n    var globalActiveClass = router.options.linkActiveClass;\n    var globalExactActiveClass = router.options.linkExactActiveClass;\n    // Support global empty active class\n    var activeClassFallback = globalActiveClass == null ? 'router-link-active' : globalActiveClass;\n    var exactActiveClassFallback = globalExactActiveClass == null ? 'router-link-exact-active' : globalExactActiveClass;\n    var activeClass = this.activeClass == null ? activeClassFallback : this.activeClass;\n    var exactActiveClass = this.exactActiveClass == null ? exactActiveClassFallback : this.exactActiveClass;\n    var compareTarget = route.redirectedFrom ? createRoute(null, normalizeLocation(route.redirectedFrom), null, router) : route;\n    classes[exactActiveClass] = isSameRoute(current, compareTarget, this.exactPath);\n    classes[activeClass] = this.exact || this.exactPath ? classes[exactActiveClass] : isIncludedRoute(current, compareTarget);\n    var ariaCurrentValue = classes[exactActiveClass] ? this.ariaCurrentValue : null;\n    var handler = function (e) {\n      if (guardEvent(e)) {\n        if (this$1$1.replace) {\n          router.replace(location, noop);\n        } else {\n          router.push(location, noop);\n        }\n      }\n    };\n    var on = {\n      click: guardEvent\n    };\n    if (Array.isArray(this.event)) {\n      this.event.forEach(function (e) {\n        on[e] = handler;\n      });\n    } else {\n      on[this.event] = handler;\n    }\n    var data = {\n      class: classes\n    };\n    var scopedSlot = !this.$scopedSlots.$hasNormal && this.$scopedSlots.default && this.$scopedSlots.default({\n      href: href,\n      route: route,\n      navigate: handler,\n      isActive: classes[activeClass],\n      isExactActive: classes[exactActiveClass]\n    });\n    if (scopedSlot) {\n      if (process.env.NODE_ENV !== 'production' && !this.custom) {\n        !warnedCustomSlot && warn(false, 'In Vue Router 4, the v-slot API will by default wrap its content with an <a> element. Use the custom prop to remove this warning:\\n<router-link v-slot=\"{ navigate, href }\" custom></router-link>\\n');\n        warnedCustomSlot = true;\n      }\n      if (scopedSlot.length === 1) {\n        return scopedSlot[0];\n      } else if (scopedSlot.length > 1 || !scopedSlot.length) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(false, \"<router-link> with to=\\\"\" + this.to + \"\\\" is trying to use a scoped slot but it didn't provide exactly one child. Wrapping the content with a span element.\");\n        }\n        return scopedSlot.length === 0 ? h() : h('span', {}, scopedSlot);\n      }\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if ('tag' in this.$options.propsData && !warnedTagProp) {\n        warn(false, \"<router-link>'s tag prop is deprecated and has been removed in Vue Router 4. Use the v-slot API to remove this warning: https://next.router.vuejs.org/guide/migration/#removal-of-event-and-tag-props-in-router-link.\");\n        warnedTagProp = true;\n      }\n      if ('event' in this.$options.propsData && !warnedEventProp) {\n        warn(false, \"<router-link>'s event prop is deprecated and has been removed in Vue Router 4. Use the v-slot API to remove this warning: https://next.router.vuejs.org/guide/migration/#removal-of-event-and-tag-props-in-router-link.\");\n        warnedEventProp = true;\n      }\n    }\n    if (this.tag === 'a') {\n      data.on = on;\n      data.attrs = {\n        href: href,\n        'aria-current': ariaCurrentValue\n      };\n    } else {\n      // find the first <a> child and apply listener and href\n      var a = findAnchor(this.$slots.default);\n      if (a) {\n        // in case the <a> is a static node\n        a.isStatic = false;\n        var aData = a.data = extend({}, a.data);\n        aData.on = aData.on || {};\n        // transform existing events in both objects into arrays so we can push later\n        for (var event in aData.on) {\n          var handler$1 = aData.on[event];\n          if (event in on) {\n            aData.on[event] = Array.isArray(handler$1) ? handler$1 : [handler$1];\n          }\n        }\n        // append new listeners for router-link\n        for (var event$1 in on) {\n          if (event$1 in aData.on) {\n            // on[event] is always a function\n            aData.on[event$1].push(on[event$1]);\n          } else {\n            aData.on[event$1] = handler;\n          }\n        }\n        var aAttrs = a.data.attrs = extend({}, a.data.attrs);\n        aAttrs.href = href;\n        aAttrs['aria-current'] = ariaCurrentValue;\n      } else {\n        // doesn't have <a> child, apply listener to self\n        data.on = on;\n      }\n    }\n    return h(this.tag, data, this.$slots.default);\n  }\n};\nfunction guardEvent(e) {\n  // don't redirect with control keys\n  if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) {\n    return;\n  }\n  // don't redirect when preventDefault called\n  if (e.defaultPrevented) {\n    return;\n  }\n  // don't redirect on right click\n  if (e.button !== undefined && e.button !== 0) {\n    return;\n  }\n  // don't redirect if `target=\"_blank\"`\n  if (e.currentTarget && e.currentTarget.getAttribute) {\n    var target = e.currentTarget.getAttribute('target');\n    if (/\\b_blank\\b/i.test(target)) {\n      return;\n    }\n  }\n  // this may be a Weex event which doesn't have this method\n  if (e.preventDefault) {\n    e.preventDefault();\n  }\n  return true;\n}\nfunction findAnchor(children) {\n  if (children) {\n    var child;\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      if (child.tag === 'a') {\n        return child;\n      }\n      if (child.children && (child = findAnchor(child.children))) {\n        return child;\n      }\n    }\n  }\n}\nvar _Vue;\nfunction install(Vue) {\n  if (install.installed && _Vue === Vue) {\n    return;\n  }\n  install.installed = true;\n  _Vue = Vue;\n  var isDef = function (v) {\n    return v !== undefined;\n  };\n  var registerInstance = function (vm, callVal) {\n    var i = vm.$options._parentVnode;\n    if (isDef(i) && isDef(i = i.data) && isDef(i = i.registerRouteInstance)) {\n      i(vm, callVal);\n    }\n  };\n  Vue.mixin({\n    beforeCreate: function beforeCreate() {\n      if (isDef(this.$options.router)) {\n        this._routerRoot = this;\n        this._router = this.$options.router;\n        this._router.init(this);\n        Vue.util.defineReactive(this, '_route', this._router.history.current);\n      } else {\n        this._routerRoot = this.$parent && this.$parent._routerRoot || this;\n      }\n      registerInstance(this, this);\n    },\n    destroyed: function destroyed() {\n      registerInstance(this);\n    }\n  });\n  Object.defineProperty(Vue.prototype, '$router', {\n    get: function get() {\n      return this._routerRoot._router;\n    }\n  });\n  Object.defineProperty(Vue.prototype, '$route', {\n    get: function get() {\n      return this._routerRoot._route;\n    }\n  });\n  Vue.component('RouterView', View);\n  Vue.component('RouterLink', Link);\n  var strats = Vue.config.optionMergeStrategies;\n  // use the same hook merging strategy for route hooks\n  strats.beforeRouteEnter = strats.beforeRouteLeave = strats.beforeRouteUpdate = strats.created;\n}\n\n/*  */\n\nvar inBrowser = typeof window !== 'undefined';\n\n/*  */\n\nfunction createRouteMap(routes, oldPathList, oldPathMap, oldNameMap, parentRoute) {\n  // the path list is used to control path matching priority\n  var pathList = oldPathList || [];\n  // $flow-disable-line\n  var pathMap = oldPathMap || Object.create(null);\n  // $flow-disable-line\n  var nameMap = oldNameMap || Object.create(null);\n  routes.forEach(function (route) {\n    addRouteRecord(pathList, pathMap, nameMap, route, parentRoute);\n  });\n\n  // ensure wildcard routes are always at the end\n  for (var i = 0, l = pathList.length; i < l; i++) {\n    if (pathList[i] === '*') {\n      pathList.push(pathList.splice(i, 1)[0]);\n      l--;\n      i--;\n    }\n  }\n  if (process.env.NODE_ENV === 'development') {\n    // warn if routes do not include leading slashes\n    var found = pathList\n    // check for missing leading slash\n    .filter(function (path) {\n      return path && path.charAt(0) !== '*' && path.charAt(0) !== '/';\n    });\n    if (found.length > 0) {\n      var pathNames = found.map(function (path) {\n        return \"- \" + path;\n      }).join('\\n');\n      warn(false, \"Non-nested routes must include a leading slash character. Fix the following routes: \\n\" + pathNames);\n    }\n  }\n  return {\n    pathList: pathList,\n    pathMap: pathMap,\n    nameMap: nameMap\n  };\n}\nfunction addRouteRecord(pathList, pathMap, nameMap, route, parent, matchAs) {\n  var path = route.path;\n  var name = route.name;\n  if (process.env.NODE_ENV !== 'production') {\n    assert(path != null, \"\\\"path\\\" is required in a route configuration.\");\n    assert(typeof route.component !== 'string', \"route config \\\"component\\\" for path: \" + String(path || name) + \" cannot be a \" + \"string id. Use an actual component instead.\");\n    warn(\n    // eslint-disable-next-line no-control-regex\n    !/[^\\u0000-\\u007F]+/.test(path), \"Route with path \\\"\" + path + \"\\\" contains unencoded characters, make sure \" + \"your path is correctly encoded before passing it to the router. Use \" + \"encodeURI to encode static segments of your path.\");\n  }\n  var pathToRegexpOptions = route.pathToRegexpOptions || {};\n  var normalizedPath = normalizePath(path, parent, pathToRegexpOptions.strict);\n  if (typeof route.caseSensitive === 'boolean') {\n    pathToRegexpOptions.sensitive = route.caseSensitive;\n  }\n  var record = {\n    path: normalizedPath,\n    regex: compileRouteRegex(normalizedPath, pathToRegexpOptions),\n    components: route.components || {\n      default: route.component\n    },\n    alias: route.alias ? typeof route.alias === 'string' ? [route.alias] : route.alias : [],\n    instances: {},\n    enteredCbs: {},\n    name: name,\n    parent: parent,\n    matchAs: matchAs,\n    redirect: route.redirect,\n    beforeEnter: route.beforeEnter,\n    meta: route.meta || {},\n    props: route.props == null ? {} : route.components ? route.props : {\n      default: route.props\n    }\n  };\n  if (route.children) {\n    // Warn if route is named, does not redirect and has a default child route.\n    // If users navigate to this route by name, the default child will\n    // not be rendered (GH Issue #629)\n    if (process.env.NODE_ENV !== 'production') {\n      if (route.name && !route.redirect && route.children.some(function (child) {\n        return /^\\/?$/.test(child.path);\n      })) {\n        warn(false, \"Named Route '\" + route.name + \"' has a default child route. \" + \"When navigating to this named route (:to=\\\"{name: '\" + route.name + \"'}\\\"), \" + \"the default child route will not be rendered. Remove the name from \" + \"this route and use the name of the default child route for named \" + \"links instead.\");\n      }\n    }\n    route.children.forEach(function (child) {\n      var childMatchAs = matchAs ? cleanPath(matchAs + \"/\" + child.path) : undefined;\n      addRouteRecord(pathList, pathMap, nameMap, child, record, childMatchAs);\n    });\n  }\n  if (!pathMap[record.path]) {\n    pathList.push(record.path);\n    pathMap[record.path] = record;\n  }\n  if (route.alias !== undefined) {\n    var aliases = Array.isArray(route.alias) ? route.alias : [route.alias];\n    for (var i = 0; i < aliases.length; ++i) {\n      var alias = aliases[i];\n      if (process.env.NODE_ENV !== 'production' && alias === path) {\n        warn(false, \"Found an alias with the same value as the path: \\\"\" + path + \"\\\". You have to remove that alias. It will be ignored in development.\");\n        // skip in dev to make it work\n        continue;\n      }\n      var aliasRoute = {\n        path: alias,\n        children: route.children\n      };\n      addRouteRecord(pathList, pathMap, nameMap, aliasRoute, parent, record.path || '/' // matchAs\n      );\n    }\n  }\n  if (name) {\n    if (!nameMap[name]) {\n      nameMap[name] = record;\n    } else if (process.env.NODE_ENV !== 'production' && !matchAs) {\n      warn(false, \"Duplicate named routes definition: \" + \"{ name: \\\"\" + name + \"\\\", path: \\\"\" + record.path + \"\\\" }\");\n    }\n  }\n}\nfunction compileRouteRegex(path, pathToRegexpOptions) {\n  var regex = pathToRegexp_1(path, [], pathToRegexpOptions);\n  if (process.env.NODE_ENV !== 'production') {\n    var keys = Object.create(null);\n    regex.keys.forEach(function (key) {\n      warn(!keys[key.name], \"Duplicate param keys in route with path: \\\"\" + path + \"\\\"\");\n      keys[key.name] = true;\n    });\n  }\n  return regex;\n}\nfunction normalizePath(path, parent, strict) {\n  if (!strict) {\n    path = path.replace(/\\/$/, '');\n  }\n  if (path[0] === '/') {\n    return path;\n  }\n  if (parent == null) {\n    return path;\n  }\n  return cleanPath(parent.path + \"/\" + path);\n}\n\n/*  */\n\nfunction createMatcher(routes, router) {\n  var ref = createRouteMap(routes);\n  var pathList = ref.pathList;\n  var pathMap = ref.pathMap;\n  var nameMap = ref.nameMap;\n  function addRoutes(routes) {\n    createRouteMap(routes, pathList, pathMap, nameMap);\n  }\n  function addRoute(parentOrRoute, route) {\n    var parent = typeof parentOrRoute !== 'object' ? nameMap[parentOrRoute] : undefined;\n    // $flow-disable-line\n    createRouteMap([route || parentOrRoute], pathList, pathMap, nameMap, parent);\n\n    // add aliases of parent\n    if (parent && parent.alias.length) {\n      createRouteMap(\n      // $flow-disable-line route is defined if parent is\n      parent.alias.map(function (alias) {\n        return {\n          path: alias,\n          children: [route]\n        };\n      }), pathList, pathMap, nameMap, parent);\n    }\n  }\n  function getRoutes() {\n    return pathList.map(function (path) {\n      return pathMap[path];\n    });\n  }\n  function match(raw, currentRoute, redirectedFrom) {\n    var location = normalizeLocation(raw, currentRoute, false, router);\n    var name = location.name;\n    if (name) {\n      var record = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        warn(record, \"Route with name '\" + name + \"' does not exist\");\n      }\n      if (!record) {\n        return _createRoute(null, location);\n      }\n      var paramNames = record.regex.keys.filter(function (key) {\n        return !key.optional;\n      }).map(function (key) {\n        return key.name;\n      });\n      if (typeof location.params !== 'object') {\n        location.params = {};\n      }\n      if (currentRoute && typeof currentRoute.params === 'object') {\n        for (var key in currentRoute.params) {\n          if (!(key in location.params) && paramNames.indexOf(key) > -1) {\n            location.params[key] = currentRoute.params[key];\n          }\n        }\n      }\n      location.path = fillParams(record.path, location.params, \"named route \\\"\" + name + \"\\\"\");\n      return _createRoute(record, location, redirectedFrom);\n    } else if (location.path) {\n      location.params = {};\n      for (var i = 0; i < pathList.length; i++) {\n        var path = pathList[i];\n        var record$1 = pathMap[path];\n        if (matchRoute(record$1.regex, location.path, location.params)) {\n          return _createRoute(record$1, location, redirectedFrom);\n        }\n      }\n    }\n    // no match\n    return _createRoute(null, location);\n  }\n  function redirect(record, location) {\n    var originalRedirect = record.redirect;\n    var redirect = typeof originalRedirect === 'function' ? originalRedirect(createRoute(record, location, null, router)) : originalRedirect;\n    if (typeof redirect === 'string') {\n      redirect = {\n        path: redirect\n      };\n    }\n    if (!redirect || typeof redirect !== 'object') {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(false, \"invalid redirect option: \" + JSON.stringify(redirect));\n      }\n      return _createRoute(null, location);\n    }\n    var re = redirect;\n    var name = re.name;\n    var path = re.path;\n    var query = location.query;\n    var hash = location.hash;\n    var params = location.params;\n    query = re.hasOwnProperty('query') ? re.query : query;\n    hash = re.hasOwnProperty('hash') ? re.hash : hash;\n    params = re.hasOwnProperty('params') ? re.params : params;\n    if (name) {\n      // resolved named direct\n      var targetRecord = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        assert(targetRecord, \"redirect failed: named route \\\"\" + name + \"\\\" not found.\");\n      }\n      return match({\n        _normalized: true,\n        name: name,\n        query: query,\n        hash: hash,\n        params: params\n      }, undefined, location);\n    } else if (path) {\n      // 1. resolve relative redirect\n      var rawPath = resolveRecordPath(path, record);\n      // 2. resolve params\n      var resolvedPath = fillParams(rawPath, params, \"redirect route with path \\\"\" + rawPath + \"\\\"\");\n      // 3. rematch with existing query and hash\n      return match({\n        _normalized: true,\n        path: resolvedPath,\n        query: query,\n        hash: hash\n      }, undefined, location);\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(false, \"invalid redirect option: \" + JSON.stringify(redirect));\n      }\n      return _createRoute(null, location);\n    }\n  }\n  function alias(record, location, matchAs) {\n    var aliasedPath = fillParams(matchAs, location.params, \"aliased route with path \\\"\" + matchAs + \"\\\"\");\n    var aliasedMatch = match({\n      _normalized: true,\n      path: aliasedPath\n    });\n    if (aliasedMatch) {\n      var matched = aliasedMatch.matched;\n      var aliasedRecord = matched[matched.length - 1];\n      location.params = aliasedMatch.params;\n      return _createRoute(aliasedRecord, location);\n    }\n    return _createRoute(null, location);\n  }\n  function _createRoute(record, location, redirectedFrom) {\n    if (record && record.redirect) {\n      return redirect(record, redirectedFrom || location);\n    }\n    if (record && record.matchAs) {\n      return alias(record, location, record.matchAs);\n    }\n    return createRoute(record, location, redirectedFrom, router);\n  }\n  return {\n    match: match,\n    addRoute: addRoute,\n    getRoutes: getRoutes,\n    addRoutes: addRoutes\n  };\n}\nfunction matchRoute(regex, path, params) {\n  var m = path.match(regex);\n  if (!m) {\n    return false;\n  } else if (!params) {\n    return true;\n  }\n  for (var i = 1, len = m.length; i < len; ++i) {\n    var key = regex.keys[i - 1];\n    if (key) {\n      // Fix #1994: using * with props: true generates a param named 0\n      params[key.name || 'pathMatch'] = typeof m[i] === 'string' ? decode(m[i]) : m[i];\n    }\n  }\n  return true;\n}\nfunction resolveRecordPath(path, record) {\n  return resolvePath(path, record.parent ? record.parent.path : '/', true);\n}\n\n/*  */\n\n// use User Timing api (if present) for more accurate key precision\nvar Time = inBrowser && window.performance && window.performance.now ? window.performance : Date;\nfunction genStateKey() {\n  return Time.now().toFixed(3);\n}\nvar _key = genStateKey();\nfunction getStateKey() {\n  return _key;\n}\nfunction setStateKey(key) {\n  return _key = key;\n}\n\n/*  */\n\nvar positionStore = Object.create(null);\nfunction setupScroll() {\n  // Prevent browser scroll behavior on History popstate\n  if ('scrollRestoration' in window.history) {\n    window.history.scrollRestoration = 'manual';\n  }\n  // Fix for #1585 for Firefox\n  // Fix for #2195 Add optional third attribute to workaround a bug in safari https://bugs.webkit.org/show_bug.cgi?id=182678\n  // Fix for #2774 Support for apps loaded from Windows file shares not mapped to network drives: replaced location.origin with\n  // window.location.protocol + '//' + window.location.host\n  // location.host contains the port and location.hostname doesn't\n  var protocolAndPath = window.location.protocol + '//' + window.location.host;\n  var absolutePath = window.location.href.replace(protocolAndPath, '');\n  // preserve existing history state as it could be overriden by the user\n  var stateCopy = extend({}, window.history.state);\n  stateCopy.key = getStateKey();\n  window.history.replaceState(stateCopy, '', absolutePath);\n  window.addEventListener('popstate', handlePopState);\n  return function () {\n    window.removeEventListener('popstate', handlePopState);\n  };\n}\nfunction handleScroll(router, to, from, isPop) {\n  if (!router.app) {\n    return;\n  }\n  var behavior = router.options.scrollBehavior;\n  if (!behavior) {\n    return;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    assert(typeof behavior === 'function', \"scrollBehavior must be a function\");\n  }\n\n  // wait until re-render finishes before scrolling\n  router.app.$nextTick(function () {\n    var position = getScrollPosition();\n    var shouldScroll = behavior.call(router, to, from, isPop ? position : null);\n    if (!shouldScroll) {\n      return;\n    }\n    if (typeof shouldScroll.then === 'function') {\n      shouldScroll.then(function (shouldScroll) {\n        scrollToPosition(shouldScroll, position);\n      }).catch(function (err) {\n        if (process.env.NODE_ENV !== 'production') {\n          assert(false, err.toString());\n        }\n      });\n    } else {\n      scrollToPosition(shouldScroll, position);\n    }\n  });\n}\nfunction saveScrollPosition() {\n  var key = getStateKey();\n  if (key) {\n    positionStore[key] = {\n      x: window.pageXOffset,\n      y: window.pageYOffset\n    };\n  }\n}\nfunction handlePopState(e) {\n  saveScrollPosition();\n  if (e.state && e.state.key) {\n    setStateKey(e.state.key);\n  }\n}\nfunction getScrollPosition() {\n  var key = getStateKey();\n  if (key) {\n    return positionStore[key];\n  }\n}\nfunction getElementPosition(el, offset) {\n  var docEl = document.documentElement;\n  var docRect = docEl.getBoundingClientRect();\n  var elRect = el.getBoundingClientRect();\n  return {\n    x: elRect.left - docRect.left - offset.x,\n    y: elRect.top - docRect.top - offset.y\n  };\n}\nfunction isValidPosition(obj) {\n  return isNumber(obj.x) || isNumber(obj.y);\n}\nfunction normalizePosition(obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : window.pageXOffset,\n    y: isNumber(obj.y) ? obj.y : window.pageYOffset\n  };\n}\nfunction normalizeOffset(obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : 0,\n    y: isNumber(obj.y) ? obj.y : 0\n  };\n}\nfunction isNumber(v) {\n  return typeof v === 'number';\n}\nvar hashStartsWithNumberRE = /^#\\d/;\nfunction scrollToPosition(shouldScroll, position) {\n  var isObject = typeof shouldScroll === 'object';\n  if (isObject && typeof shouldScroll.selector === 'string') {\n    // getElementById would still fail if the selector contains a more complicated query like #main[data-attr]\n    // but at the same time, it doesn't make much sense to select an element with an id and an extra selector\n    var el = hashStartsWithNumberRE.test(shouldScroll.selector) // $flow-disable-line\n    ? document.getElementById(shouldScroll.selector.slice(1)) // $flow-disable-line\n    : document.querySelector(shouldScroll.selector);\n    if (el) {\n      var offset = shouldScroll.offset && typeof shouldScroll.offset === 'object' ? shouldScroll.offset : {};\n      offset = normalizeOffset(offset);\n      position = getElementPosition(el, offset);\n    } else if (isValidPosition(shouldScroll)) {\n      position = normalizePosition(shouldScroll);\n    }\n  } else if (isObject && isValidPosition(shouldScroll)) {\n    position = normalizePosition(shouldScroll);\n  }\n  if (position) {\n    // $flow-disable-line\n    if ('scrollBehavior' in document.documentElement.style) {\n      window.scrollTo({\n        left: position.x,\n        top: position.y,\n        // $flow-disable-line\n        behavior: shouldScroll.behavior\n      });\n    } else {\n      window.scrollTo(position.x, position.y);\n    }\n  }\n}\n\n/*  */\n\nvar supportsPushState = inBrowser && function () {\n  var ua = window.navigator.userAgent;\n  if ((ua.indexOf('Android 2.') !== -1 || ua.indexOf('Android 4.0') !== -1) && ua.indexOf('Mobile Safari') !== -1 && ua.indexOf('Chrome') === -1 && ua.indexOf('Windows Phone') === -1) {\n    return false;\n  }\n  return window.history && typeof window.history.pushState === 'function';\n}();\nfunction pushState(url, replace) {\n  saveScrollPosition();\n  // try...catch the pushState call to get around Safari\n  // DOM Exception 18 where it limits to 100 pushState calls\n  var history = window.history;\n  try {\n    if (replace) {\n      // preserve existing history state as it could be overriden by the user\n      var stateCopy = extend({}, history.state);\n      stateCopy.key = getStateKey();\n      history.replaceState(stateCopy, '', url);\n    } else {\n      history.pushState({\n        key: setStateKey(genStateKey())\n      }, '', url);\n    }\n  } catch (e) {\n    window.location[replace ? 'replace' : 'assign'](url);\n  }\n}\nfunction replaceState(url) {\n  pushState(url, true);\n}\n\n// When changing thing, also edit router.d.ts\nvar NavigationFailureType = {\n  redirected: 2,\n  aborted: 4,\n  cancelled: 8,\n  duplicated: 16\n};\nfunction createNavigationRedirectedError(from, to) {\n  return createRouterError(from, to, NavigationFailureType.redirected, \"Redirected when going from \\\"\" + from.fullPath + \"\\\" to \\\"\" + stringifyRoute(to) + \"\\\" via a navigation guard.\");\n}\nfunction createNavigationDuplicatedError(from, to) {\n  var error = createRouterError(from, to, NavigationFailureType.duplicated, \"Avoided redundant navigation to current location: \\\"\" + from.fullPath + \"\\\".\");\n  // backwards compatible with the first introduction of Errors\n  error.name = 'NavigationDuplicated';\n  return error;\n}\nfunction createNavigationCancelledError(from, to) {\n  return createRouterError(from, to, NavigationFailureType.cancelled, \"Navigation cancelled from \\\"\" + from.fullPath + \"\\\" to \\\"\" + to.fullPath + \"\\\" with a new navigation.\");\n}\nfunction createNavigationAbortedError(from, to) {\n  return createRouterError(from, to, NavigationFailureType.aborted, \"Navigation aborted from \\\"\" + from.fullPath + \"\\\" to \\\"\" + to.fullPath + \"\\\" via a navigation guard.\");\n}\nfunction createRouterError(from, to, type, message) {\n  var error = new Error(message);\n  error._isRouter = true;\n  error.from = from;\n  error.to = to;\n  error.type = type;\n  return error;\n}\nvar propertiesToLog = ['params', 'query', 'hash'];\nfunction stringifyRoute(to) {\n  if (typeof to === 'string') {\n    return to;\n  }\n  if ('path' in to) {\n    return to.path;\n  }\n  var location = {};\n  propertiesToLog.forEach(function (key) {\n    if (key in to) {\n      location[key] = to[key];\n    }\n  });\n  return JSON.stringify(location, null, 2);\n}\nfunction isError(err) {\n  return Object.prototype.toString.call(err).indexOf('Error') > -1;\n}\nfunction isNavigationFailure(err, errorType) {\n  return isError(err) && err._isRouter && (errorType == null || err.type === errorType);\n}\n\n/*  */\n\nfunction runQueue(queue, fn, cb) {\n  var step = function (index) {\n    if (index >= queue.length) {\n      cb();\n    } else {\n      if (queue[index]) {\n        fn(queue[index], function () {\n          step(index + 1);\n        });\n      } else {\n        step(index + 1);\n      }\n    }\n  };\n  step(0);\n}\n\n/*  */\n\nfunction resolveAsyncComponents(matched) {\n  return function (to, from, next) {\n    var hasAsync = false;\n    var pending = 0;\n    var error = null;\n    flatMapComponents(matched, function (def, _, match, key) {\n      // if it's a function and doesn't have cid attached,\n      // assume it's an async component resolve function.\n      // we are not using Vue's default async resolving mechanism because\n      // we want to halt the navigation until the incoming component has been\n      // resolved.\n      if (typeof def === 'function' && def.cid === undefined) {\n        hasAsync = true;\n        pending++;\n        var resolve = once(function (resolvedDef) {\n          if (isESModule(resolvedDef)) {\n            resolvedDef = resolvedDef.default;\n          }\n          // save resolved on async factory in case it's used elsewhere\n          def.resolved = typeof resolvedDef === 'function' ? resolvedDef : _Vue.extend(resolvedDef);\n          match.components[key] = resolvedDef;\n          pending--;\n          if (pending <= 0) {\n            next();\n          }\n        });\n        var reject = once(function (reason) {\n          var msg = \"Failed to resolve async component \" + key + \": \" + reason;\n          process.env.NODE_ENV !== 'production' && warn(false, msg);\n          if (!error) {\n            error = isError(reason) ? reason : new Error(msg);\n            next(error);\n          }\n        });\n        var res;\n        try {\n          res = def(resolve, reject);\n        } catch (e) {\n          reject(e);\n        }\n        if (res) {\n          if (typeof res.then === 'function') {\n            res.then(resolve, reject);\n          } else {\n            // new syntax in Vue 2.3\n            var comp = res.component;\n            if (comp && typeof comp.then === 'function') {\n              comp.then(resolve, reject);\n            }\n          }\n        }\n      }\n    });\n    if (!hasAsync) {\n      next();\n    }\n  };\n}\nfunction flatMapComponents(matched, fn) {\n  return flatten(matched.map(function (m) {\n    return Object.keys(m.components).map(function (key) {\n      return fn(m.components[key], m.instances[key], m, key);\n    });\n  }));\n}\nfunction flatten(arr) {\n  return Array.prototype.concat.apply([], arr);\n}\nvar hasSymbol = typeof Symbol === 'function' && typeof Symbol.toStringTag === 'symbol';\nfunction isESModule(obj) {\n  return obj.__esModule || hasSymbol && obj[Symbol.toStringTag] === 'Module';\n}\n\n// in Webpack 2, require.ensure now also returns a Promise\n// so the resolve/reject functions may get called an extra time\n// if the user uses an arrow function shorthand that happens to\n// return that Promise.\nfunction once(fn) {\n  var called = false;\n  return function () {\n    var args = [],\n      len = arguments.length;\n    while (len--) args[len] = arguments[len];\n    if (called) {\n      return;\n    }\n    called = true;\n    return fn.apply(this, args);\n  };\n}\n\n/*  */\n\nvar History = function History(router, base) {\n  this.router = router;\n  this.base = normalizeBase(base);\n  // start with a route object that stands for \"nowhere\"\n  this.current = START;\n  this.pending = null;\n  this.ready = false;\n  this.readyCbs = [];\n  this.readyErrorCbs = [];\n  this.errorCbs = [];\n  this.listeners = [];\n};\nHistory.prototype.listen = function listen(cb) {\n  this.cb = cb;\n};\nHistory.prototype.onReady = function onReady(cb, errorCb) {\n  if (this.ready) {\n    cb();\n  } else {\n    this.readyCbs.push(cb);\n    if (errorCb) {\n      this.readyErrorCbs.push(errorCb);\n    }\n  }\n};\nHistory.prototype.onError = function onError(errorCb) {\n  this.errorCbs.push(errorCb);\n};\nHistory.prototype.transitionTo = function transitionTo(location, onComplete, onAbort) {\n  var this$1$1 = this;\n  var route;\n  // catch redirect option https://github.com/vuejs/vue-router/issues/3201\n  try {\n    route = this.router.match(location, this.current);\n  } catch (e) {\n    this.errorCbs.forEach(function (cb) {\n      cb(e);\n    });\n    // Exception should still be thrown\n    throw e;\n  }\n  var prev = this.current;\n  this.confirmTransition(route, function () {\n    this$1$1.updateRoute(route);\n    onComplete && onComplete(route);\n    this$1$1.ensureURL();\n    this$1$1.router.afterHooks.forEach(function (hook) {\n      hook && hook(route, prev);\n    });\n\n    // fire ready cbs once\n    if (!this$1$1.ready) {\n      this$1$1.ready = true;\n      this$1$1.readyCbs.forEach(function (cb) {\n        cb(route);\n      });\n    }\n  }, function (err) {\n    if (onAbort) {\n      onAbort(err);\n    }\n    if (err && !this$1$1.ready) {\n      // Initial redirection should not mark the history as ready yet\n      // because it's triggered by the redirection instead\n      // https://github.com/vuejs/vue-router/issues/3225\n      // https://github.com/vuejs/vue-router/issues/3331\n      if (!isNavigationFailure(err, NavigationFailureType.redirected) || prev !== START) {\n        this$1$1.ready = true;\n        this$1$1.readyErrorCbs.forEach(function (cb) {\n          cb(err);\n        });\n      }\n    }\n  });\n};\nHistory.prototype.confirmTransition = function confirmTransition(route, onComplete, onAbort) {\n  var this$1$1 = this;\n  var current = this.current;\n  this.pending = route;\n  var abort = function (err) {\n    // changed after adding errors with\n    // https://github.com/vuejs/vue-router/pull/3047 before that change,\n    // redirect and aborted navigation would produce an err == null\n    if (!isNavigationFailure(err) && isError(err)) {\n      if (this$1$1.errorCbs.length) {\n        this$1$1.errorCbs.forEach(function (cb) {\n          cb(err);\n        });\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(false, 'uncaught error during route navigation:');\n        }\n        console.error(err);\n      }\n    }\n    onAbort && onAbort(err);\n  };\n  var lastRouteIndex = route.matched.length - 1;\n  var lastCurrentIndex = current.matched.length - 1;\n  if (isSameRoute(route, current) &&\n  // in the case the route map has been dynamically appended to\n  lastRouteIndex === lastCurrentIndex && route.matched[lastRouteIndex] === current.matched[lastCurrentIndex]) {\n    this.ensureURL();\n    if (route.hash) {\n      handleScroll(this.router, current, route, false);\n    }\n    return abort(createNavigationDuplicatedError(current, route));\n  }\n  var ref = resolveQueue(this.current.matched, route.matched);\n  var updated = ref.updated;\n  var deactivated = ref.deactivated;\n  var activated = ref.activated;\n  var queue = [].concat(\n  // in-component leave guards\n  extractLeaveGuards(deactivated),\n  // global before hooks\n  this.router.beforeHooks,\n  // in-component update hooks\n  extractUpdateHooks(updated),\n  // in-config enter guards\n  activated.map(function (m) {\n    return m.beforeEnter;\n  }),\n  // async components\n  resolveAsyncComponents(activated));\n  var iterator = function (hook, next) {\n    if (this$1$1.pending !== route) {\n      return abort(createNavigationCancelledError(current, route));\n    }\n    try {\n      hook(route, current, function (to) {\n        if (to === false) {\n          // next(false) -> abort navigation, ensure current URL\n          this$1$1.ensureURL(true);\n          abort(createNavigationAbortedError(current, route));\n        } else if (isError(to)) {\n          this$1$1.ensureURL(true);\n          abort(to);\n        } else if (typeof to === 'string' || typeof to === 'object' && (typeof to.path === 'string' || typeof to.name === 'string')) {\n          // next('/') or next({ path: '/' }) -> redirect\n          abort(createNavigationRedirectedError(current, route));\n          if (typeof to === 'object' && to.replace) {\n            this$1$1.replace(to);\n          } else {\n            this$1$1.push(to);\n          }\n        } else {\n          // confirm transition and pass on the value\n          next(to);\n        }\n      });\n    } catch (e) {\n      abort(e);\n    }\n  };\n  runQueue(queue, iterator, function () {\n    // wait until async components are resolved before\n    // extracting in-component enter guards\n    var enterGuards = extractEnterGuards(activated);\n    var queue = enterGuards.concat(this$1$1.router.resolveHooks);\n    runQueue(queue, iterator, function () {\n      if (this$1$1.pending !== route) {\n        return abort(createNavigationCancelledError(current, route));\n      }\n      this$1$1.pending = null;\n      onComplete(route);\n      if (this$1$1.router.app) {\n        this$1$1.router.app.$nextTick(function () {\n          handleRouteEntered(route);\n        });\n      }\n    });\n  });\n};\nHistory.prototype.updateRoute = function updateRoute(route) {\n  this.current = route;\n  this.cb && this.cb(route);\n};\nHistory.prototype.setupListeners = function setupListeners() {\n  // Default implementation is empty\n};\nHistory.prototype.teardown = function teardown() {\n  // clean up event listeners\n  // https://github.com/vuejs/vue-router/issues/2341\n  this.listeners.forEach(function (cleanupListener) {\n    cleanupListener();\n  });\n  this.listeners = [];\n\n  // reset current history route\n  // https://github.com/vuejs/vue-router/issues/3294\n  this.current = START;\n  this.pending = null;\n};\nfunction normalizeBase(base) {\n  if (!base) {\n    if (inBrowser) {\n      // respect <base> tag\n      var baseEl = document.querySelector('base');\n      base = baseEl && baseEl.getAttribute('href') || '/';\n      // strip full URL origin\n      base = base.replace(/^https?:\\/\\/[^\\/]+/, '');\n    } else {\n      base = '/';\n    }\n  }\n  // make sure there's the starting slash\n  if (base.charAt(0) !== '/') {\n    base = '/' + base;\n  }\n  // remove trailing slash\n  return base.replace(/\\/$/, '');\n}\nfunction resolveQueue(current, next) {\n  var i;\n  var max = Math.max(current.length, next.length);\n  for (i = 0; i < max; i++) {\n    if (current[i] !== next[i]) {\n      break;\n    }\n  }\n  return {\n    updated: next.slice(0, i),\n    activated: next.slice(i),\n    deactivated: current.slice(i)\n  };\n}\nfunction extractGuards(records, name, bind, reverse) {\n  var guards = flatMapComponents(records, function (def, instance, match, key) {\n    var guard = extractGuard(def, name);\n    if (guard) {\n      return Array.isArray(guard) ? guard.map(function (guard) {\n        return bind(guard, instance, match, key);\n      }) : bind(guard, instance, match, key);\n    }\n  });\n  return flatten(reverse ? guards.reverse() : guards);\n}\nfunction extractGuard(def, key) {\n  if (typeof def !== 'function') {\n    // extend now so that global mixins are applied.\n    def = _Vue.extend(def);\n  }\n  return def.options[key];\n}\nfunction extractLeaveGuards(deactivated) {\n  return extractGuards(deactivated, 'beforeRouteLeave', bindGuard, true);\n}\nfunction extractUpdateHooks(updated) {\n  return extractGuards(updated, 'beforeRouteUpdate', bindGuard);\n}\nfunction bindGuard(guard, instance) {\n  if (instance) {\n    return function boundRouteGuard() {\n      return guard.apply(instance, arguments);\n    };\n  }\n}\nfunction extractEnterGuards(activated) {\n  return extractGuards(activated, 'beforeRouteEnter', function (guard, _, match, key) {\n    return bindEnterGuard(guard, match, key);\n  });\n}\nfunction bindEnterGuard(guard, match, key) {\n  return function routeEnterGuard(to, from, next) {\n    return guard(to, from, function (cb) {\n      if (typeof cb === 'function') {\n        if (!match.enteredCbs[key]) {\n          match.enteredCbs[key] = [];\n        }\n        match.enteredCbs[key].push(cb);\n      }\n      next(cb);\n    });\n  };\n}\n\n/*  */\n\nvar HTML5History = /*@__PURE__*/function (History) {\n  function HTML5History(router, base) {\n    History.call(this, router, base);\n    this._startLocation = getLocation(this.base);\n  }\n  if (History) HTML5History.__proto__ = History;\n  HTML5History.prototype = Object.create(History && History.prototype);\n  HTML5History.prototype.constructor = HTML5History;\n  HTML5History.prototype.setupListeners = function setupListeners() {\n    var this$1$1 = this;\n    if (this.listeners.length > 0) {\n      return;\n    }\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n    var handleRoutingEvent = function () {\n      var current = this$1$1.current;\n\n      // Avoiding first `popstate` event dispatched in some browsers but first\n      // history route not updated since async guard at the same time.\n      var location = getLocation(this$1$1.base);\n      if (this$1$1.current === START && location === this$1$1._startLocation) {\n        return;\n      }\n      this$1$1.transitionTo(location, function (route) {\n        if (supportsScroll) {\n          handleScroll(router, route, current, true);\n        }\n      });\n    };\n    window.addEventListener('popstate', handleRoutingEvent);\n    this.listeners.push(function () {\n      window.removeEventListener('popstate', handleRoutingEvent);\n    });\n  };\n  HTML5History.prototype.go = function go(n) {\n    window.history.go(n);\n  };\n  HTML5History.prototype.push = function push(location, onComplete, onAbort) {\n    var this$1$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      pushState(cleanPath(this$1$1.base + route.fullPath));\n      handleScroll(this$1$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  HTML5History.prototype.replace = function replace(location, onComplete, onAbort) {\n    var this$1$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      replaceState(cleanPath(this$1$1.base + route.fullPath));\n      handleScroll(this$1$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  HTML5History.prototype.ensureURL = function ensureURL(push) {\n    if (getLocation(this.base) !== this.current.fullPath) {\n      var current = cleanPath(this.base + this.current.fullPath);\n      push ? pushState(current) : replaceState(current);\n    }\n  };\n  HTML5History.prototype.getCurrentLocation = function getCurrentLocation() {\n    return getLocation(this.base);\n  };\n  return HTML5History;\n}(History);\nfunction getLocation(base) {\n  var path = window.location.pathname;\n  var pathLowerCase = path.toLowerCase();\n  var baseLowerCase = base.toLowerCase();\n  // base=\"/a\" shouldn't turn path=\"/app\" into \"/a/pp\"\n  // https://github.com/vuejs/vue-router/issues/3555\n  // so we ensure the trailing slash in the base\n  if (base && (pathLowerCase === baseLowerCase || pathLowerCase.indexOf(cleanPath(baseLowerCase + '/')) === 0)) {\n    path = path.slice(base.length);\n  }\n  return (path || '/') + window.location.search + window.location.hash;\n}\n\n/*  */\n\nvar HashHistory = /*@__PURE__*/function (History) {\n  function HashHistory(router, base, fallback) {\n    History.call(this, router, base);\n    // check history fallback deeplinking\n    if (fallback && checkFallback(this.base)) {\n      return;\n    }\n    ensureSlash();\n  }\n  if (History) HashHistory.__proto__ = History;\n  HashHistory.prototype = Object.create(History && History.prototype);\n  HashHistory.prototype.constructor = HashHistory;\n\n  // this is delayed until the app mounts\n  // to avoid the hashchange listener being fired too early\n  HashHistory.prototype.setupListeners = function setupListeners() {\n    var this$1$1 = this;\n    if (this.listeners.length > 0) {\n      return;\n    }\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n    var handleRoutingEvent = function () {\n      var current = this$1$1.current;\n      if (!ensureSlash()) {\n        return;\n      }\n      this$1$1.transitionTo(getHash(), function (route) {\n        if (supportsScroll) {\n          handleScroll(this$1$1.router, route, current, true);\n        }\n        if (!supportsPushState) {\n          replaceHash(route.fullPath);\n        }\n      });\n    };\n    var eventType = supportsPushState ? 'popstate' : 'hashchange';\n    window.addEventListener(eventType, handleRoutingEvent);\n    this.listeners.push(function () {\n      window.removeEventListener(eventType, handleRoutingEvent);\n    });\n  };\n  HashHistory.prototype.push = function push(location, onComplete, onAbort) {\n    var this$1$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      pushHash(route.fullPath);\n      handleScroll(this$1$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  HashHistory.prototype.replace = function replace(location, onComplete, onAbort) {\n    var this$1$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      replaceHash(route.fullPath);\n      handleScroll(this$1$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  HashHistory.prototype.go = function go(n) {\n    window.history.go(n);\n  };\n  HashHistory.prototype.ensureURL = function ensureURL(push) {\n    var current = this.current.fullPath;\n    if (getHash() !== current) {\n      push ? pushHash(current) : replaceHash(current);\n    }\n  };\n  HashHistory.prototype.getCurrentLocation = function getCurrentLocation() {\n    return getHash();\n  };\n  return HashHistory;\n}(History);\nfunction checkFallback(base) {\n  var location = getLocation(base);\n  if (!/^\\/#/.test(location)) {\n    window.location.replace(cleanPath(base + '/#' + location));\n    return true;\n  }\n}\nfunction ensureSlash() {\n  var path = getHash();\n  if (path.charAt(0) === '/') {\n    return true;\n  }\n  replaceHash('/' + path);\n  return false;\n}\nfunction getHash() {\n  // We can't use window.location.hash here because it's not\n  // consistent across browsers - Firefox will pre-decode it!\n  var href = window.location.href;\n  var index = href.indexOf('#');\n  // empty path\n  if (index < 0) {\n    return '';\n  }\n  href = href.slice(index + 1);\n  return href;\n}\nfunction getUrl(path) {\n  var href = window.location.href;\n  var i = href.indexOf('#');\n  var base = i >= 0 ? href.slice(0, i) : href;\n  return base + \"#\" + path;\n}\nfunction pushHash(path) {\n  if (supportsPushState) {\n    pushState(getUrl(path));\n  } else {\n    window.location.hash = path;\n  }\n}\nfunction replaceHash(path) {\n  if (supportsPushState) {\n    replaceState(getUrl(path));\n  } else {\n    window.location.replace(getUrl(path));\n  }\n}\n\n/*  */\n\nvar AbstractHistory = /*@__PURE__*/function (History) {\n  function AbstractHistory(router, base) {\n    History.call(this, router, base);\n    this.stack = [];\n    this.index = -1;\n  }\n  if (History) AbstractHistory.__proto__ = History;\n  AbstractHistory.prototype = Object.create(History && History.prototype);\n  AbstractHistory.prototype.constructor = AbstractHistory;\n  AbstractHistory.prototype.push = function push(location, onComplete, onAbort) {\n    var this$1$1 = this;\n    this.transitionTo(location, function (route) {\n      this$1$1.stack = this$1$1.stack.slice(0, this$1$1.index + 1).concat(route);\n      this$1$1.index++;\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  AbstractHistory.prototype.replace = function replace(location, onComplete, onAbort) {\n    var this$1$1 = this;\n    this.transitionTo(location, function (route) {\n      this$1$1.stack = this$1$1.stack.slice(0, this$1$1.index).concat(route);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  AbstractHistory.prototype.go = function go(n) {\n    var this$1$1 = this;\n    var targetIndex = this.index + n;\n    if (targetIndex < 0 || targetIndex >= this.stack.length) {\n      return;\n    }\n    var route = this.stack[targetIndex];\n    this.confirmTransition(route, function () {\n      var prev = this$1$1.current;\n      this$1$1.index = targetIndex;\n      this$1$1.updateRoute(route);\n      this$1$1.router.afterHooks.forEach(function (hook) {\n        hook && hook(route, prev);\n      });\n    }, function (err) {\n      if (isNavigationFailure(err, NavigationFailureType.duplicated)) {\n        this$1$1.index = targetIndex;\n      }\n    });\n  };\n  AbstractHistory.prototype.getCurrentLocation = function getCurrentLocation() {\n    var current = this.stack[this.stack.length - 1];\n    return current ? current.fullPath : '/';\n  };\n  AbstractHistory.prototype.ensureURL = function ensureURL() {\n    // noop\n  };\n  return AbstractHistory;\n}(History);\n\n/*  */\n\nvar VueRouter = function VueRouter(options) {\n  if (options === void 0) options = {};\n  if (process.env.NODE_ENV !== 'production') {\n    warn(this instanceof VueRouter, \"Router must be called with the new operator.\");\n  }\n  this.app = null;\n  this.apps = [];\n  this.options = options;\n  this.beforeHooks = [];\n  this.resolveHooks = [];\n  this.afterHooks = [];\n  this.matcher = createMatcher(options.routes || [], this);\n  var mode = options.mode || 'hash';\n  this.fallback = mode === 'history' && !supportsPushState && options.fallback !== false;\n  if (this.fallback) {\n    mode = 'hash';\n  }\n  if (!inBrowser) {\n    mode = 'abstract';\n  }\n  this.mode = mode;\n  switch (mode) {\n    case 'history':\n      this.history = new HTML5History(this, options.base);\n      break;\n    case 'hash':\n      this.history = new HashHistory(this, options.base, this.fallback);\n      break;\n    case 'abstract':\n      this.history = new AbstractHistory(this, options.base);\n      break;\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        assert(false, \"invalid mode: \" + mode);\n      }\n  }\n};\nvar prototypeAccessors = {\n  currentRoute: {\n    configurable: true\n  }\n};\nVueRouter.prototype.match = function match(raw, current, redirectedFrom) {\n  return this.matcher.match(raw, current, redirectedFrom);\n};\nprototypeAccessors.currentRoute.get = function () {\n  return this.history && this.history.current;\n};\nVueRouter.prototype.init = function init(app /* Vue component instance */) {\n  var this$1$1 = this;\n  process.env.NODE_ENV !== 'production' && assert(install.installed, \"not installed. Make sure to call `Vue.use(VueRouter)` \" + \"before creating root instance.\");\n  this.apps.push(app);\n\n  // set up app destroyed handler\n  // https://github.com/vuejs/vue-router/issues/2639\n  app.$once('hook:destroyed', function () {\n    // clean out app from this.apps array once destroyed\n    var index = this$1$1.apps.indexOf(app);\n    if (index > -1) {\n      this$1$1.apps.splice(index, 1);\n    }\n    // ensure we still have a main app or null if no apps\n    // we do not release the router so it can be reused\n    if (this$1$1.app === app) {\n      this$1$1.app = this$1$1.apps[0] || null;\n    }\n    if (!this$1$1.app) {\n      this$1$1.history.teardown();\n    }\n  });\n\n  // main app previously initialized\n  // return as we don't need to set up new history listener\n  if (this.app) {\n    return;\n  }\n  this.app = app;\n  var history = this.history;\n  if (history instanceof HTML5History || history instanceof HashHistory) {\n    var handleInitialScroll = function (routeOrError) {\n      var from = history.current;\n      var expectScroll = this$1$1.options.scrollBehavior;\n      var supportsScroll = supportsPushState && expectScroll;\n      if (supportsScroll && 'fullPath' in routeOrError) {\n        handleScroll(this$1$1, routeOrError, from, false);\n      }\n    };\n    var setupListeners = function (routeOrError) {\n      history.setupListeners();\n      handleInitialScroll(routeOrError);\n    };\n    history.transitionTo(history.getCurrentLocation(), setupListeners, setupListeners);\n  }\n  history.listen(function (route) {\n    this$1$1.apps.forEach(function (app) {\n      app._route = route;\n    });\n  });\n};\nVueRouter.prototype.beforeEach = function beforeEach(fn) {\n  return registerHook(this.beforeHooks, fn);\n};\nVueRouter.prototype.beforeResolve = function beforeResolve(fn) {\n  return registerHook(this.resolveHooks, fn);\n};\nVueRouter.prototype.afterEach = function afterEach(fn) {\n  return registerHook(this.afterHooks, fn);\n};\nVueRouter.prototype.onReady = function onReady(cb, errorCb) {\n  this.history.onReady(cb, errorCb);\n};\nVueRouter.prototype.onError = function onError(errorCb) {\n  this.history.onError(errorCb);\n};\nVueRouter.prototype.push = function push(location, onComplete, onAbort) {\n  var this$1$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1$1.history.push(location, resolve, reject);\n    });\n  } else {\n    this.history.push(location, onComplete, onAbort);\n  }\n};\nVueRouter.prototype.replace = function replace(location, onComplete, onAbort) {\n  var this$1$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1$1.history.replace(location, resolve, reject);\n    });\n  } else {\n    this.history.replace(location, onComplete, onAbort);\n  }\n};\nVueRouter.prototype.go = function go(n) {\n  this.history.go(n);\n};\nVueRouter.prototype.back = function back() {\n  this.go(-1);\n};\nVueRouter.prototype.forward = function forward() {\n  this.go(1);\n};\nVueRouter.prototype.getMatchedComponents = function getMatchedComponents(to) {\n  var route = to ? to.matched ? to : this.resolve(to).route : this.currentRoute;\n  if (!route) {\n    return [];\n  }\n  return [].concat.apply([], route.matched.map(function (m) {\n    return Object.keys(m.components).map(function (key) {\n      return m.components[key];\n    });\n  }));\n};\nVueRouter.prototype.resolve = function resolve(to, current, append) {\n  current = current || this.history.current;\n  var location = normalizeLocation(to, current, append, this);\n  var route = this.match(location, current);\n  var fullPath = route.redirectedFrom || route.fullPath;\n  var base = this.history.base;\n  var href = createHref(base, fullPath, this.mode);\n  return {\n    location: location,\n    route: route,\n    href: href,\n    // for backwards compat\n    normalizedTo: location,\n    resolved: route\n  };\n};\nVueRouter.prototype.getRoutes = function getRoutes() {\n  return this.matcher.getRoutes();\n};\nVueRouter.prototype.addRoute = function addRoute(parentOrRoute, route) {\n  this.matcher.addRoute(parentOrRoute, route);\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\nVueRouter.prototype.addRoutes = function addRoutes(routes) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn(false, 'router.addRoutes() is deprecated and has been removed in Vue Router 4. Use router.addRoute() instead.');\n  }\n  this.matcher.addRoutes(routes);\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\nObject.defineProperties(VueRouter.prototype, prototypeAccessors);\nvar VueRouter$1 = VueRouter;\nfunction registerHook(list, fn) {\n  list.push(fn);\n  return function () {\n    var i = list.indexOf(fn);\n    if (i > -1) {\n      list.splice(i, 1);\n    }\n  };\n}\nfunction createHref(base, fullPath, mode) {\n  var path = mode === 'hash' ? '#' + fullPath : fullPath;\n  return base ? cleanPath(base + '/' + path) : path;\n}\n\n// We cannot remove this as it would be a breaking change\nVueRouter.install = install;\nVueRouter.version = '3.6.5';\nVueRouter.isNavigationFailure = isNavigationFailure;\nVueRouter.NavigationFailureType = NavigationFailureType;\nVueRouter.START_LOCATION = START;\nif (inBrowser && window.Vue) {\n  window.Vue.use(VueRouter);\n}\nvar version = '3.6.5';\nexport { NavigationFailureType, Link as RouterLink, View as RouterView, START as START_LOCATION, VueRouter$1 as default, isNavigationFailure, version };", "map": {"version": 3, "names": ["assert", "condition", "message", "Error", "warn", "console", "extend", "a", "b", "key", "encodeReserveRE", "encodeReserveReplacer", "c", "charCodeAt", "toString", "commaRE", "encode", "str", "encodeURIComponent", "replace", "decode", "decodeURIComponent", "err", "process", "env", "NODE_ENV", "<PERSON><PERSON><PERSON><PERSON>", "query", "extraQuery", "_parseQuery", "parse", "parse<PERSON><PERSON>y", "parsed<PERSON><PERSON><PERSON>", "e", "value", "Array", "isArray", "map", "castQueryParamValue", "String", "res", "trim", "split", "for<PERSON>ach", "param", "parts", "shift", "val", "length", "join", "undefined", "push", "stringifyQuery", "obj", "Object", "keys", "result", "val2", "filter", "x", "trailingSlashRE", "createRoute", "record", "location", "redirectedFrom", "router", "options", "clone", "route", "name", "meta", "path", "hash", "params", "fullPath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matched", "formatMatch", "freeze", "START", "unshift", "parent", "ref", "_stringifyQuery", "stringify", "isSameRoute", "only<PERSON><PERSON>", "isObjectEqual", "a<PERSON><PERSON><PERSON>", "sort", "b<PERSON><PERSON><PERSON>", "every", "i", "aVal", "b<PERSON><PERSON>", "bVal", "isIncludedRoute", "current", "target", "indexOf", "queryIncludes", "handleRouteEntered", "instances", "instance", "cbs", "enteredCbs", "i$1", "_isBeingDestroyed", "View", "functional", "props", "type", "default", "render", "_", "children", "data", "routerView", "h", "$createElement", "$route", "cache", "_routerViewCache", "depth", "inactive", "_routerRoot", "vnodeData", "$vnode", "keepAlive", "_directInactive", "_inactive", "$parent", "routerViewDepth", "cachedData", "cachedComponent", "component", "configProps", "fillPropsinData", "components", "registerRouteInstance", "vm", "hook", "prepatch", "vnode", "componentInstance", "init", "propsToPass", "resolveProps", "attrs", "config", "<PERSON><PERSON><PERSON>", "relative", "base", "append", "firstChar", "char<PERSON>t", "stack", "pop", "segments", "segment", "parsePath", "hashIndex", "slice", "queryIndex", "cleanPath", "isarray", "arr", "prototype", "call", "pathToRegexp_1", "pathToRegexp", "parse_1", "compile_1", "compile", "tokensToFunction_1", "tokensToFunction", "tokensToRegExp_1", "tokensToRegExp", "PATH_REGEXP", "RegExp", "tokens", "index", "defaultDelimiter", "delimiter", "exec", "m", "escaped", "offset", "next", "prefix", "capture", "group", "modifier", "asterisk", "partial", "repeat", "optional", "pattern", "escapeGroup", "escapeString", "substr", "encodeURIComponentPretty", "encodeURI", "toUpperCase", "encodeAsterisk", "matches", "flags", "opts", "pretty", "token", "TypeError", "JSON", "j", "test", "attachKeys", "re", "sensitive", "regexpToRegexp", "groups", "source", "match", "arrayToRegexp", "regexp", "stringToRegexp", "strict", "end", "endsWithDelimiter", "regexpCompileCache", "create", "fillParams", "routeMsg", "filler", "pathMatch", "normalizeLocation", "raw", "_normalized", "params$1", "rawPath", "parsed<PERSON><PERSON>", "basePath", "toTypes", "eventTypes", "noop", "warnedCustomSlot", "warnedTagProp", "warnedEventProp", "Link", "to", "required", "tag", "custom", "Boolean", "exact", "exactPath", "activeClass", "exactActiveClass", "ariaCurrentValue", "event", "this$1$1", "$router", "resolve", "href", "classes", "globalActiveClass", "linkActiveClass", "globalExactActiveClass", "linkExactActiveClass", "activeClassFallback", "exactActiveClassFallback", "compareTarget", "handler", "guardEvent", "on", "click", "class", "scopedSlot", "$scopedSlots", "$hasNormal", "navigate", "isActive", "isExactActive", "$options", "propsData", "findAnchor", "$slots", "isStatic", "aData", "handler$1", "event$1", "aAttrs", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "defaultPrevented", "button", "currentTarget", "getAttribute", "preventDefault", "child", "_Vue", "install", "<PERSON><PERSON>", "installed", "isDef", "v", "registerInstance", "callVal", "_parentVnode", "mixin", "beforeCreate", "_router", "util", "defineReactive", "history", "destroyed", "defineProperty", "get", "_route", "strats", "optionMergeStrategies", "beforeRouteEnter", "beforeRouteLeave", "beforeRouteUpdate", "created", "inBrowser", "window", "createRouteMap", "routes", "oldPathList", "oldPathMap", "oldNameMap", "parentRoute", "pathList", "pathMap", "nameMap", "addRouteRecord", "l", "splice", "found", "pathNames", "matchAs", "pathToRegexpOptions", "normalizedPath", "normalizePath", "caseSensitive", "regex", "compileRouteRegex", "alias", "redirect", "beforeEnter", "some", "childMatchAs", "aliases", "<PERSON><PERSON><PERSON><PERSON>", "createMatcher", "addRoutes", "addRoute", "parentOrRoute", "getRoutes", "currentRoute", "_createRoute", "paramNames", "record$1", "matchRoute", "originalRedirect", "hasOwnProperty", "targetRecord", "resolveRecordPath", "<PERSON><PERSON><PERSON>", "alias<PERSON><PERSON><PERSON>", "alias<PERSON><PERSON><PERSON>", "alias<PERSON><PERSON><PERSON><PERSON>", "len", "Time", "performance", "now", "Date", "genStateKey", "toFixed", "_key", "getStateKey", "setStateKey", "positionStore", "setupScroll", "scrollRestoration", "protocolAndPath", "protocol", "host", "absolutePath", "stateCopy", "state", "replaceState", "addEventListener", "handlePopState", "removeEventListener", "handleScroll", "from", "isPop", "app", "behavior", "scroll<PERSON>eh<PERSON>or", "$nextTick", "position", "getScrollPosition", "shouldScroll", "then", "scrollToPosition", "catch", "saveScrollPosition", "pageXOffset", "y", "pageYOffset", "getElementPosition", "el", "docEl", "document", "documentElement", "docRect", "getBoundingClientRect", "elRect", "left", "top", "isValidPosition", "isNumber", "normalizePosition", "normalizeOffset", "hashStartsWithNumberRE", "isObject", "selector", "getElementById", "querySelector", "style", "scrollTo", "supportsPushState", "ua", "navigator", "userAgent", "pushState", "url", "NavigationFailureType", "redirected", "aborted", "cancelled", "duplicated", "createNavigationRedirectedError", "createRouterError", "stringifyRoute", "createNavigationDuplicatedError", "error", "createNavigationCancelledError", "createNavigationAbortedError", "_isRouter", "propertiesToLog", "isError", "isNavigationFailure", "errorType", "runQueue", "queue", "fn", "cb", "step", "resolveAsyncComponents", "has<PERSON><PERSON>", "pending", "flatMapComponents", "def", "cid", "once", "resolvedDef", "isESModule", "resolved", "reject", "reason", "msg", "comp", "flatten", "concat", "apply", "hasSymbol", "Symbol", "toStringTag", "__esModule", "called", "args", "arguments", "History", "normalizeBase", "ready", "readyCbs", "readyErrorCbs", "errorCbs", "listeners", "listen", "onReady", "errorCb", "onError", "transitionTo", "onComplete", "onAbort", "prev", "confirmTransition", "updateRoute", "ensureURL", "afterHooks", "abort", "lastRouteIndex", "lastCurrentIndex", "resolveQueue", "updated", "deactivated", "activated", "extractLeaveGuards", "before<PERSON>ooks", "extractUpdateHooks", "iterator", "enterGuards", "extractEnterGuards", "resolve<PERSON>ooks", "setupListeners", "teardown", "cleanupListener", "baseEl", "max", "Math", "extractGuards", "records", "bind", "reverse", "guards", "guard", "extractGuard", "<PERSON><PERSON><PERSON>", "boundRouteGuard", "bindEnterGuard", "routeEnterGuard", "HTML5History", "_startLocation", "getLocation", "__proto__", "constructor", "expectScroll", "supportsScroll", "handleRoutingEvent", "go", "n", "fromRoute", "getCurrentLocation", "pathname", "pathLowerCase", "toLowerCase", "baseLowerCase", "search", "HashHistory", "fallback", "checkFallback", "ensureSlash", "getHash", "replaceHash", "eventType", "pushHash", "getUrl", "AbstractHistory", "targetIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apps", "matcher", "mode", "prototypeAccessors", "configurable", "$once", "handleInitialScroll", "routeOrError", "beforeEach", "registerHook", "beforeResolve", "after<PERSON>ach", "Promise", "back", "forward", "getMatchedComponents", "createHref", "normalizedTo", "defineProperties", "VueRouter$1", "list", "version", "START_LOCATION", "use", "RouterLink", "RouterView"], "sources": ["C:/Users/<USER>/Desktop/2025-5-11项目/渡一教育/duyivue/myapp/node_modules/vue-router/dist/vue-router.esm.js"], "sourcesContent": ["/*!\n  * vue-router v3.6.5\n  * (c) 2022 Evan You\n  * @license MIT\n  */\n/*  */\n\nfunction assert (condition, message) {\n  if (!condition) {\n    throw new Error((\"[vue-router] \" + message))\n  }\n}\n\nfunction warn (condition, message) {\n  if (!condition) {\n    typeof console !== 'undefined' && console.warn((\"[vue-router] \" + message));\n  }\n}\n\nfunction extend (a, b) {\n  for (var key in b) {\n    a[key] = b[key];\n  }\n  return a\n}\n\n/*  */\n\nvar encodeReserveRE = /[!'()*]/g;\nvar encodeReserveReplacer = function (c) { return '%' + c.charCodeAt(0).toString(16); };\nvar commaRE = /%2C/g;\n\n// fixed encodeURIComponent which is more conformant to RFC3986:\n// - escapes [!'()*]\n// - preserve commas\nvar encode = function (str) { return encodeURIComponent(str)\n    .replace(encodeReserveRE, encodeReserveReplacer)\n    .replace(commaRE, ','); };\n\nfunction decode (str) {\n  try {\n    return decodeURIComponent(str)\n  } catch (err) {\n    if (process.env.NODE_ENV !== 'production') {\n      warn(false, (\"Error decoding \\\"\" + str + \"\\\". Leaving it intact.\"));\n    }\n  }\n  return str\n}\n\nfunction resolveQuery (\n  query,\n  extraQuery,\n  _parseQuery\n) {\n  if ( extraQuery === void 0 ) extraQuery = {};\n\n  var parse = _parseQuery || parseQuery;\n  var parsedQuery;\n  try {\n    parsedQuery = parse(query || '');\n  } catch (e) {\n    process.env.NODE_ENV !== 'production' && warn(false, e.message);\n    parsedQuery = {};\n  }\n  for (var key in extraQuery) {\n    var value = extraQuery[key];\n    parsedQuery[key] = Array.isArray(value)\n      ? value.map(castQueryParamValue)\n      : castQueryParamValue(value);\n  }\n  return parsedQuery\n}\n\nvar castQueryParamValue = function (value) { return (value == null || typeof value === 'object' ? value : String(value)); };\n\nfunction parseQuery (query) {\n  var res = {};\n\n  query = query.trim().replace(/^(\\?|#|&)/, '');\n\n  if (!query) {\n    return res\n  }\n\n  query.split('&').forEach(function (param) {\n    var parts = param.replace(/\\+/g, ' ').split('=');\n    var key = decode(parts.shift());\n    var val = parts.length > 0 ? decode(parts.join('=')) : null;\n\n    if (res[key] === undefined) {\n      res[key] = val;\n    } else if (Array.isArray(res[key])) {\n      res[key].push(val);\n    } else {\n      res[key] = [res[key], val];\n    }\n  });\n\n  return res\n}\n\nfunction stringifyQuery (obj) {\n  var res = obj\n    ? Object.keys(obj)\n      .map(function (key) {\n        var val = obj[key];\n\n        if (val === undefined) {\n          return ''\n        }\n\n        if (val === null) {\n          return encode(key)\n        }\n\n        if (Array.isArray(val)) {\n          var result = [];\n          val.forEach(function (val2) {\n            if (val2 === undefined) {\n              return\n            }\n            if (val2 === null) {\n              result.push(encode(key));\n            } else {\n              result.push(encode(key) + '=' + encode(val2));\n            }\n          });\n          return result.join('&')\n        }\n\n        return encode(key) + '=' + encode(val)\n      })\n      .filter(function (x) { return x.length > 0; })\n      .join('&')\n    : null;\n  return res ? (\"?\" + res) : ''\n}\n\n/*  */\n\nvar trailingSlashRE = /\\/?$/;\n\nfunction createRoute (\n  record,\n  location,\n  redirectedFrom,\n  router\n) {\n  var stringifyQuery = router && router.options.stringifyQuery;\n\n  var query = location.query || {};\n  try {\n    query = clone(query);\n  } catch (e) {}\n\n  var route = {\n    name: location.name || (record && record.name),\n    meta: (record && record.meta) || {},\n    path: location.path || '/',\n    hash: location.hash || '',\n    query: query,\n    params: location.params || {},\n    fullPath: getFullPath(location, stringifyQuery),\n    matched: record ? formatMatch(record) : []\n  };\n  if (redirectedFrom) {\n    route.redirectedFrom = getFullPath(redirectedFrom, stringifyQuery);\n  }\n  return Object.freeze(route)\n}\n\nfunction clone (value) {\n  if (Array.isArray(value)) {\n    return value.map(clone)\n  } else if (value && typeof value === 'object') {\n    var res = {};\n    for (var key in value) {\n      res[key] = clone(value[key]);\n    }\n    return res\n  } else {\n    return value\n  }\n}\n\n// the starting route that represents the initial state\nvar START = createRoute(null, {\n  path: '/'\n});\n\nfunction formatMatch (record) {\n  var res = [];\n  while (record) {\n    res.unshift(record);\n    record = record.parent;\n  }\n  return res\n}\n\nfunction getFullPath (\n  ref,\n  _stringifyQuery\n) {\n  var path = ref.path;\n  var query = ref.query; if ( query === void 0 ) query = {};\n  var hash = ref.hash; if ( hash === void 0 ) hash = '';\n\n  var stringify = _stringifyQuery || stringifyQuery;\n  return (path || '/') + stringify(query) + hash\n}\n\nfunction isSameRoute (a, b, onlyPath) {\n  if (b === START) {\n    return a === b\n  } else if (!b) {\n    return false\n  } else if (a.path && b.path) {\n    return a.path.replace(trailingSlashRE, '') === b.path.replace(trailingSlashRE, '') && (onlyPath ||\n      a.hash === b.hash &&\n      isObjectEqual(a.query, b.query))\n  } else if (a.name && b.name) {\n    return (\n      a.name === b.name &&\n      (onlyPath || (\n        a.hash === b.hash &&\n      isObjectEqual(a.query, b.query) &&\n      isObjectEqual(a.params, b.params))\n      )\n    )\n  } else {\n    return false\n  }\n}\n\nfunction isObjectEqual (a, b) {\n  if ( a === void 0 ) a = {};\n  if ( b === void 0 ) b = {};\n\n  // handle null value #1566\n  if (!a || !b) { return a === b }\n  var aKeys = Object.keys(a).sort();\n  var bKeys = Object.keys(b).sort();\n  if (aKeys.length !== bKeys.length) {\n    return false\n  }\n  return aKeys.every(function (key, i) {\n    var aVal = a[key];\n    var bKey = bKeys[i];\n    if (bKey !== key) { return false }\n    var bVal = b[key];\n    // query values can be null and undefined\n    if (aVal == null || bVal == null) { return aVal === bVal }\n    // check nested equality\n    if (typeof aVal === 'object' && typeof bVal === 'object') {\n      return isObjectEqual(aVal, bVal)\n    }\n    return String(aVal) === String(bVal)\n  })\n}\n\nfunction isIncludedRoute (current, target) {\n  return (\n    current.path.replace(trailingSlashRE, '/').indexOf(\n      target.path.replace(trailingSlashRE, '/')\n    ) === 0 &&\n    (!target.hash || current.hash === target.hash) &&\n    queryIncludes(current.query, target.query)\n  )\n}\n\nfunction queryIncludes (current, target) {\n  for (var key in target) {\n    if (!(key in current)) {\n      return false\n    }\n  }\n  return true\n}\n\nfunction handleRouteEntered (route) {\n  for (var i = 0; i < route.matched.length; i++) {\n    var record = route.matched[i];\n    for (var name in record.instances) {\n      var instance = record.instances[name];\n      var cbs = record.enteredCbs[name];\n      if (!instance || !cbs) { continue }\n      delete record.enteredCbs[name];\n      for (var i$1 = 0; i$1 < cbs.length; i$1++) {\n        if (!instance._isBeingDestroyed) { cbs[i$1](instance); }\n      }\n    }\n  }\n}\n\nvar View = {\n  name: 'RouterView',\n  functional: true,\n  props: {\n    name: {\n      type: String,\n      default: 'default'\n    }\n  },\n  render: function render (_, ref) {\n    var props = ref.props;\n    var children = ref.children;\n    var parent = ref.parent;\n    var data = ref.data;\n\n    // used by devtools to display a router-view badge\n    data.routerView = true;\n\n    // directly use parent context's createElement() function\n    // so that components rendered by router-view can resolve named slots\n    var h = parent.$createElement;\n    var name = props.name;\n    var route = parent.$route;\n    var cache = parent._routerViewCache || (parent._routerViewCache = {});\n\n    // determine current view depth, also check to see if the tree\n    // has been toggled inactive but kept-alive.\n    var depth = 0;\n    var inactive = false;\n    while (parent && parent._routerRoot !== parent) {\n      var vnodeData = parent.$vnode ? parent.$vnode.data : {};\n      if (vnodeData.routerView) {\n        depth++;\n      }\n      if (vnodeData.keepAlive && parent._directInactive && parent._inactive) {\n        inactive = true;\n      }\n      parent = parent.$parent;\n    }\n    data.routerViewDepth = depth;\n\n    // render previous view if the tree is inactive and kept-alive\n    if (inactive) {\n      var cachedData = cache[name];\n      var cachedComponent = cachedData && cachedData.component;\n      if (cachedComponent) {\n        // #2301\n        // pass props\n        if (cachedData.configProps) {\n          fillPropsinData(cachedComponent, data, cachedData.route, cachedData.configProps);\n        }\n        return h(cachedComponent, data, children)\n      } else {\n        // render previous empty view\n        return h()\n      }\n    }\n\n    var matched = route.matched[depth];\n    var component = matched && matched.components[name];\n\n    // render empty node if no matched route or no config component\n    if (!matched || !component) {\n      cache[name] = null;\n      return h()\n    }\n\n    // cache component\n    cache[name] = { component: component };\n\n    // attach instance registration hook\n    // this will be called in the instance's injected lifecycle hooks\n    data.registerRouteInstance = function (vm, val) {\n      // val could be undefined for unregistration\n      var current = matched.instances[name];\n      if (\n        (val && current !== vm) ||\n        (!val && current === vm)\n      ) {\n        matched.instances[name] = val;\n      }\n    }\n\n    // also register instance in prepatch hook\n    // in case the same component instance is reused across different routes\n    ;(data.hook || (data.hook = {})).prepatch = function (_, vnode) {\n      matched.instances[name] = vnode.componentInstance;\n    };\n\n    // register instance in init hook\n    // in case kept-alive component be actived when routes changed\n    data.hook.init = function (vnode) {\n      if (vnode.data.keepAlive &&\n        vnode.componentInstance &&\n        vnode.componentInstance !== matched.instances[name]\n      ) {\n        matched.instances[name] = vnode.componentInstance;\n      }\n\n      // if the route transition has already been confirmed then we weren't\n      // able to call the cbs during confirmation as the component was not\n      // registered yet, so we call it here.\n      handleRouteEntered(route);\n    };\n\n    var configProps = matched.props && matched.props[name];\n    // save route and configProps in cache\n    if (configProps) {\n      extend(cache[name], {\n        route: route,\n        configProps: configProps\n      });\n      fillPropsinData(component, data, route, configProps);\n    }\n\n    return h(component, data, children)\n  }\n};\n\nfunction fillPropsinData (component, data, route, configProps) {\n  // resolve props\n  var propsToPass = data.props = resolveProps(route, configProps);\n  if (propsToPass) {\n    // clone to prevent mutation\n    propsToPass = data.props = extend({}, propsToPass);\n    // pass non-declared props as attrs\n    var attrs = data.attrs = data.attrs || {};\n    for (var key in propsToPass) {\n      if (!component.props || !(key in component.props)) {\n        attrs[key] = propsToPass[key];\n        delete propsToPass[key];\n      }\n    }\n  }\n}\n\nfunction resolveProps (route, config) {\n  switch (typeof config) {\n    case 'undefined':\n      return\n    case 'object':\n      return config\n    case 'function':\n      return config(route)\n    case 'boolean':\n      return config ? route.params : undefined\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        warn(\n          false,\n          \"props in \\\"\" + (route.path) + \"\\\" is a \" + (typeof config) + \", \" +\n          \"expecting an object, function or boolean.\"\n        );\n      }\n  }\n}\n\n/*  */\n\nfunction resolvePath (\n  relative,\n  base,\n  append\n) {\n  var firstChar = relative.charAt(0);\n  if (firstChar === '/') {\n    return relative\n  }\n\n  if (firstChar === '?' || firstChar === '#') {\n    return base + relative\n  }\n\n  var stack = base.split('/');\n\n  // remove trailing segment if:\n  // - not appending\n  // - appending to trailing slash (last segment is empty)\n  if (!append || !stack[stack.length - 1]) {\n    stack.pop();\n  }\n\n  // resolve relative path\n  var segments = relative.replace(/^\\//, '').split('/');\n  for (var i = 0; i < segments.length; i++) {\n    var segment = segments[i];\n    if (segment === '..') {\n      stack.pop();\n    } else if (segment !== '.') {\n      stack.push(segment);\n    }\n  }\n\n  // ensure leading slash\n  if (stack[0] !== '') {\n    stack.unshift('');\n  }\n\n  return stack.join('/')\n}\n\nfunction parsePath (path) {\n  var hash = '';\n  var query = '';\n\n  var hashIndex = path.indexOf('#');\n  if (hashIndex >= 0) {\n    hash = path.slice(hashIndex);\n    path = path.slice(0, hashIndex);\n  }\n\n  var queryIndex = path.indexOf('?');\n  if (queryIndex >= 0) {\n    query = path.slice(queryIndex + 1);\n    path = path.slice(0, queryIndex);\n  }\n\n  return {\n    path: path,\n    query: query,\n    hash: hash\n  }\n}\n\nfunction cleanPath (path) {\n  return path.replace(/\\/(?:\\s*\\/)+/g, '/')\n}\n\nvar isarray = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};\n\n/**\n * Expose `pathToRegexp`.\n */\nvar pathToRegexp_1 = pathToRegexp;\nvar parse_1 = parse;\nvar compile_1 = compile;\nvar tokensToFunction_1 = tokensToFunction;\nvar tokensToRegExp_1 = tokensToRegExp;\n\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\nvar PATH_REGEXP = new RegExp([\n  // Match escaped characters that would otherwise appear in future matches.\n  // This allows the user to escape special characters that won't transform.\n  '(\\\\\\\\.)',\n  // Match Express-style parameters and un-named parameters with a prefix\n  // and optional suffixes. Matches appear as:\n  //\n  // \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n  // \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n  // \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n  '([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?|(\\\\*))'\n].join('|'), 'g');\n\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {string}  str\n * @param  {Object=} options\n * @return {!Array}\n */\nfunction parse (str, options) {\n  var tokens = [];\n  var key = 0;\n  var index = 0;\n  var path = '';\n  var defaultDelimiter = options && options.delimiter || '/';\n  var res;\n\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0];\n    var escaped = res[1];\n    var offset = res.index;\n    path += str.slice(index, offset);\n    index = offset + m.length;\n\n    // Ignore already escaped sequences.\n    if (escaped) {\n      path += escaped[1];\n      continue\n    }\n\n    var next = str[index];\n    var prefix = res[2];\n    var name = res[3];\n    var capture = res[4];\n    var group = res[5];\n    var modifier = res[6];\n    var asterisk = res[7];\n\n    // Push the current path onto the tokens.\n    if (path) {\n      tokens.push(path);\n      path = '';\n    }\n\n    var partial = prefix != null && next != null && next !== prefix;\n    var repeat = modifier === '+' || modifier === '*';\n    var optional = modifier === '?' || modifier === '*';\n    var delimiter = res[2] || defaultDelimiter;\n    var pattern = capture || group;\n\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      partial: partial,\n      asterisk: !!asterisk,\n      pattern: pattern ? escapeGroup(pattern) : (asterisk ? '.*' : '[^' + escapeString(delimiter) + ']+?')\n    });\n  }\n\n  // Match any characters still remaining.\n  if (index < str.length) {\n    path += str.substr(index);\n  }\n\n  // If the path exists, push it onto the end.\n  if (path) {\n    tokens.push(path);\n  }\n\n  return tokens\n}\n\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {string}             str\n * @param  {Object=}            options\n * @return {!function(Object=, Object=)}\n */\nfunction compile (str, options) {\n  return tokensToFunction(parse(str, options), options)\n}\n\n/**\n * Prettier encoding of URI path segments.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeURIComponentPretty (str) {\n  return encodeURI(str).replace(/[\\/?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Encode the asterisk parameter. Similar to `pretty`, but allows slashes.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeAsterisk (str) {\n  return encodeURI(str).replace(/[?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nfunction tokensToFunction (tokens, options) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length);\n\n  // Compile all the patterns before compilation.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$', flags(options));\n    }\n  }\n\n  return function (obj, opts) {\n    var path = '';\n    var data = obj || {};\n    var options = opts || {};\n    var encode = options.pretty ? encodeURIComponentPretty : encodeURIComponent;\n\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n\n      if (typeof token === 'string') {\n        path += token;\n\n        continue\n      }\n\n      var value = data[token.name];\n      var segment;\n\n      if (value == null) {\n        if (token.optional) {\n          // Prepend partial segment prefixes.\n          if (token.partial) {\n            path += token.prefix;\n          }\n\n          continue\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined')\n        }\n      }\n\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received `' + JSON.stringify(value) + '`')\n        }\n\n        if (value.length === 0) {\n          if (token.optional) {\n            continue\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty')\n          }\n        }\n\n        for (var j = 0; j < value.length; j++) {\n          segment = encode(value[j]);\n\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received `' + JSON.stringify(segment) + '`')\n          }\n\n          path += (j === 0 ? token.prefix : token.delimiter) + segment;\n        }\n\n        continue\n      }\n\n      segment = token.asterisk ? encodeAsterisk(value) : encode(value);\n\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"')\n      }\n\n      path += token.prefix + segment;\n    }\n\n    return path\n  }\n}\n\n/**\n * Escape a regular expression string.\n *\n * @param  {string} str\n * @return {string}\n */\nfunction escapeString (str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/\\\\])/g, '\\\\$1')\n}\n\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {string} group\n * @return {string}\n */\nfunction escapeGroup (group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1')\n}\n\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {!RegExp} re\n * @param  {Array}   keys\n * @return {!RegExp}\n */\nfunction attachKeys (re, keys) {\n  re.keys = keys;\n  return re\n}\n\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {string}\n */\nfunction flags (options) {\n  return options && options.sensitive ? '' : 'i'\n}\n\n/**\n * Pull out keys from a regexp.\n *\n * @param  {!RegExp} path\n * @param  {!Array}  keys\n * @return {!RegExp}\n */\nfunction regexpToRegexp (path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g);\n\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        partial: false,\n        asterisk: false,\n        pattern: null\n      });\n    }\n  }\n\n  return attachKeys(path, keys)\n}\n\n/**\n * Transform an array into a regexp.\n *\n * @param  {!Array}  path\n * @param  {Array}   keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction arrayToRegexp (path, keys, options) {\n  var parts = [];\n\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source);\n  }\n\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options));\n\n  return attachKeys(regexp, keys)\n}\n\n/**\n * Create a path regexp from string input.\n *\n * @param  {string}  path\n * @param  {!Array}  keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction stringToRegexp (path, keys, options) {\n  return tokensToRegExp(parse(path, options), keys, options)\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {!Array}          tokens\n * @param  {(Array|Object)=} keys\n * @param  {Object=}         options\n * @return {!RegExp}\n */\nfunction tokensToRegExp (tokens, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options);\n    keys = [];\n  }\n\n  options = options || {};\n\n  var strict = options.strict;\n  var end = options.end !== false;\n  var route = '';\n\n  // Iterate over the tokens and create our regexp string.\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i];\n\n    if (typeof token === 'string') {\n      route += escapeString(token);\n    } else {\n      var prefix = escapeString(token.prefix);\n      var capture = '(?:' + token.pattern + ')';\n\n      keys.push(token);\n\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*';\n      }\n\n      if (token.optional) {\n        if (!token.partial) {\n          capture = '(?:' + prefix + '(' + capture + '))?';\n        } else {\n          capture = prefix + '(' + capture + ')?';\n        }\n      } else {\n        capture = prefix + '(' + capture + ')';\n      }\n\n      route += capture;\n    }\n  }\n\n  var delimiter = escapeString(options.delimiter || '/');\n  var endsWithDelimiter = route.slice(-delimiter.length) === delimiter;\n\n  // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n  if (!strict) {\n    route = (endsWithDelimiter ? route.slice(0, -delimiter.length) : route) + '(?:' + delimiter + '(?=$))?';\n  }\n\n  if (end) {\n    route += '$';\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithDelimiter ? '' : '(?=' + delimiter + '|$)';\n  }\n\n  return attachKeys(new RegExp('^' + route, flags(options)), keys)\n}\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(string|RegExp|Array)} path\n * @param  {(Array|Object)=}       keys\n * @param  {Object=}               options\n * @return {!RegExp}\n */\nfunction pathToRegexp (path, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options);\n    keys = [];\n  }\n\n  options = options || {};\n\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path, /** @type {!Array} */ (keys))\n  }\n\n  if (isarray(path)) {\n    return arrayToRegexp(/** @type {!Array} */ (path), /** @type {!Array} */ (keys), options)\n  }\n\n  return stringToRegexp(/** @type {string} */ (path), /** @type {!Array} */ (keys), options)\n}\npathToRegexp_1.parse = parse_1;\npathToRegexp_1.compile = compile_1;\npathToRegexp_1.tokensToFunction = tokensToFunction_1;\npathToRegexp_1.tokensToRegExp = tokensToRegExp_1;\n\n/*  */\n\n// $flow-disable-line\nvar regexpCompileCache = Object.create(null);\n\nfunction fillParams (\n  path,\n  params,\n  routeMsg\n) {\n  params = params || {};\n  try {\n    var filler =\n      regexpCompileCache[path] ||\n      (regexpCompileCache[path] = pathToRegexp_1.compile(path));\n\n    // Fix #2505 resolving asterisk routes { name: 'not-found', params: { pathMatch: '/not-found' }}\n    // and fix #3106 so that you can work with location descriptor object having params.pathMatch equal to empty string\n    if (typeof params.pathMatch === 'string') { params[0] = params.pathMatch; }\n\n    return filler(params, { pretty: true })\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      // Fix #3072 no warn if `pathMatch` is string\n      warn(typeof params.pathMatch === 'string', (\"missing param for \" + routeMsg + \": \" + (e.message)));\n    }\n    return ''\n  } finally {\n    // delete the 0 if it was added\n    delete params[0];\n  }\n}\n\n/*  */\n\nfunction normalizeLocation (\n  raw,\n  current,\n  append,\n  router\n) {\n  var next = typeof raw === 'string' ? { path: raw } : raw;\n  // named target\n  if (next._normalized) {\n    return next\n  } else if (next.name) {\n    next = extend({}, raw);\n    var params = next.params;\n    if (params && typeof params === 'object') {\n      next.params = extend({}, params);\n    }\n    return next\n  }\n\n  // relative params\n  if (!next.path && next.params && current) {\n    next = extend({}, next);\n    next._normalized = true;\n    var params$1 = extend(extend({}, current.params), next.params);\n    if (current.name) {\n      next.name = current.name;\n      next.params = params$1;\n    } else if (current.matched.length) {\n      var rawPath = current.matched[current.matched.length - 1].path;\n      next.path = fillParams(rawPath, params$1, (\"path \" + (current.path)));\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn(false, \"relative params navigation requires a current route.\");\n    }\n    return next\n  }\n\n  var parsedPath = parsePath(next.path || '');\n  var basePath = (current && current.path) || '/';\n  var path = parsedPath.path\n    ? resolvePath(parsedPath.path, basePath, append || next.append)\n    : basePath;\n\n  var query = resolveQuery(\n    parsedPath.query,\n    next.query,\n    router && router.options.parseQuery\n  );\n\n  var hash = next.hash || parsedPath.hash;\n  if (hash && hash.charAt(0) !== '#') {\n    hash = \"#\" + hash;\n  }\n\n  return {\n    _normalized: true,\n    path: path,\n    query: query,\n    hash: hash\n  }\n}\n\n/*  */\n\n// work around weird flow bug\nvar toTypes = [String, Object];\nvar eventTypes = [String, Array];\n\nvar noop = function () {};\n\nvar warnedCustomSlot;\nvar warnedTagProp;\nvar warnedEventProp;\n\nvar Link = {\n  name: 'RouterLink',\n  props: {\n    to: {\n      type: toTypes,\n      required: true\n    },\n    tag: {\n      type: String,\n      default: 'a'\n    },\n    custom: Boolean,\n    exact: Boolean,\n    exactPath: Boolean,\n    append: Boolean,\n    replace: Boolean,\n    activeClass: String,\n    exactActiveClass: String,\n    ariaCurrentValue: {\n      type: String,\n      default: 'page'\n    },\n    event: {\n      type: eventTypes,\n      default: 'click'\n    }\n  },\n  render: function render (h) {\n    var this$1$1 = this;\n\n    var router = this.$router;\n    var current = this.$route;\n    var ref = router.resolve(\n      this.to,\n      current,\n      this.append\n    );\n    var location = ref.location;\n    var route = ref.route;\n    var href = ref.href;\n\n    var classes = {};\n    var globalActiveClass = router.options.linkActiveClass;\n    var globalExactActiveClass = router.options.linkExactActiveClass;\n    // Support global empty active class\n    var activeClassFallback =\n      globalActiveClass == null ? 'router-link-active' : globalActiveClass;\n    var exactActiveClassFallback =\n      globalExactActiveClass == null\n        ? 'router-link-exact-active'\n        : globalExactActiveClass;\n    var activeClass =\n      this.activeClass == null ? activeClassFallback : this.activeClass;\n    var exactActiveClass =\n      this.exactActiveClass == null\n        ? exactActiveClassFallback\n        : this.exactActiveClass;\n\n    var compareTarget = route.redirectedFrom\n      ? createRoute(null, normalizeLocation(route.redirectedFrom), null, router)\n      : route;\n\n    classes[exactActiveClass] = isSameRoute(current, compareTarget, this.exactPath);\n    classes[activeClass] = this.exact || this.exactPath\n      ? classes[exactActiveClass]\n      : isIncludedRoute(current, compareTarget);\n\n    var ariaCurrentValue = classes[exactActiveClass] ? this.ariaCurrentValue : null;\n\n    var handler = function (e) {\n      if (guardEvent(e)) {\n        if (this$1$1.replace) {\n          router.replace(location, noop);\n        } else {\n          router.push(location, noop);\n        }\n      }\n    };\n\n    var on = { click: guardEvent };\n    if (Array.isArray(this.event)) {\n      this.event.forEach(function (e) {\n        on[e] = handler;\n      });\n    } else {\n      on[this.event] = handler;\n    }\n\n    var data = { class: classes };\n\n    var scopedSlot =\n      !this.$scopedSlots.$hasNormal &&\n      this.$scopedSlots.default &&\n      this.$scopedSlots.default({\n        href: href,\n        route: route,\n        navigate: handler,\n        isActive: classes[activeClass],\n        isExactActive: classes[exactActiveClass]\n      });\n\n    if (scopedSlot) {\n      if (process.env.NODE_ENV !== 'production' && !this.custom) {\n        !warnedCustomSlot && warn(false, 'In Vue Router 4, the v-slot API will by default wrap its content with an <a> element. Use the custom prop to remove this warning:\\n<router-link v-slot=\"{ navigate, href }\" custom></router-link>\\n');\n        warnedCustomSlot = true;\n      }\n      if (scopedSlot.length === 1) {\n        return scopedSlot[0]\n      } else if (scopedSlot.length > 1 || !scopedSlot.length) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(\n            false,\n            (\"<router-link> with to=\\\"\" + (this.to) + \"\\\" is trying to use a scoped slot but it didn't provide exactly one child. Wrapping the content with a span element.\")\n          );\n        }\n        return scopedSlot.length === 0 ? h() : h('span', {}, scopedSlot)\n      }\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if ('tag' in this.$options.propsData && !warnedTagProp) {\n        warn(\n          false,\n          \"<router-link>'s tag prop is deprecated and has been removed in Vue Router 4. Use the v-slot API to remove this warning: https://next.router.vuejs.org/guide/migration/#removal-of-event-and-tag-props-in-router-link.\"\n        );\n        warnedTagProp = true;\n      }\n      if ('event' in this.$options.propsData && !warnedEventProp) {\n        warn(\n          false,\n          \"<router-link>'s event prop is deprecated and has been removed in Vue Router 4. Use the v-slot API to remove this warning: https://next.router.vuejs.org/guide/migration/#removal-of-event-and-tag-props-in-router-link.\"\n        );\n        warnedEventProp = true;\n      }\n    }\n\n    if (this.tag === 'a') {\n      data.on = on;\n      data.attrs = { href: href, 'aria-current': ariaCurrentValue };\n    } else {\n      // find the first <a> child and apply listener and href\n      var a = findAnchor(this.$slots.default);\n      if (a) {\n        // in case the <a> is a static node\n        a.isStatic = false;\n        var aData = (a.data = extend({}, a.data));\n        aData.on = aData.on || {};\n        // transform existing events in both objects into arrays so we can push later\n        for (var event in aData.on) {\n          var handler$1 = aData.on[event];\n          if (event in on) {\n            aData.on[event] = Array.isArray(handler$1) ? handler$1 : [handler$1];\n          }\n        }\n        // append new listeners for router-link\n        for (var event$1 in on) {\n          if (event$1 in aData.on) {\n            // on[event] is always a function\n            aData.on[event$1].push(on[event$1]);\n          } else {\n            aData.on[event$1] = handler;\n          }\n        }\n\n        var aAttrs = (a.data.attrs = extend({}, a.data.attrs));\n        aAttrs.href = href;\n        aAttrs['aria-current'] = ariaCurrentValue;\n      } else {\n        // doesn't have <a> child, apply listener to self\n        data.on = on;\n      }\n    }\n\n    return h(this.tag, data, this.$slots.default)\n  }\n};\n\nfunction guardEvent (e) {\n  // don't redirect with control keys\n  if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) { return }\n  // don't redirect when preventDefault called\n  if (e.defaultPrevented) { return }\n  // don't redirect on right click\n  if (e.button !== undefined && e.button !== 0) { return }\n  // don't redirect if `target=\"_blank\"`\n  if (e.currentTarget && e.currentTarget.getAttribute) {\n    var target = e.currentTarget.getAttribute('target');\n    if (/\\b_blank\\b/i.test(target)) { return }\n  }\n  // this may be a Weex event which doesn't have this method\n  if (e.preventDefault) {\n    e.preventDefault();\n  }\n  return true\n}\n\nfunction findAnchor (children) {\n  if (children) {\n    var child;\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      if (child.tag === 'a') {\n        return child\n      }\n      if (child.children && (child = findAnchor(child.children))) {\n        return child\n      }\n    }\n  }\n}\n\nvar _Vue;\n\nfunction install (Vue) {\n  if (install.installed && _Vue === Vue) { return }\n  install.installed = true;\n\n  _Vue = Vue;\n\n  var isDef = function (v) { return v !== undefined; };\n\n  var registerInstance = function (vm, callVal) {\n    var i = vm.$options._parentVnode;\n    if (isDef(i) && isDef(i = i.data) && isDef(i = i.registerRouteInstance)) {\n      i(vm, callVal);\n    }\n  };\n\n  Vue.mixin({\n    beforeCreate: function beforeCreate () {\n      if (isDef(this.$options.router)) {\n        this._routerRoot = this;\n        this._router = this.$options.router;\n        this._router.init(this);\n        Vue.util.defineReactive(this, '_route', this._router.history.current);\n      } else {\n        this._routerRoot = (this.$parent && this.$parent._routerRoot) || this;\n      }\n      registerInstance(this, this);\n    },\n    destroyed: function destroyed () {\n      registerInstance(this);\n    }\n  });\n\n  Object.defineProperty(Vue.prototype, '$router', {\n    get: function get () { return this._routerRoot._router }\n  });\n\n  Object.defineProperty(Vue.prototype, '$route', {\n    get: function get () { return this._routerRoot._route }\n  });\n\n  Vue.component('RouterView', View);\n  Vue.component('RouterLink', Link);\n\n  var strats = Vue.config.optionMergeStrategies;\n  // use the same hook merging strategy for route hooks\n  strats.beforeRouteEnter = strats.beforeRouteLeave = strats.beforeRouteUpdate = strats.created;\n}\n\n/*  */\n\nvar inBrowser = typeof window !== 'undefined';\n\n/*  */\n\nfunction createRouteMap (\n  routes,\n  oldPathList,\n  oldPathMap,\n  oldNameMap,\n  parentRoute\n) {\n  // the path list is used to control path matching priority\n  var pathList = oldPathList || [];\n  // $flow-disable-line\n  var pathMap = oldPathMap || Object.create(null);\n  // $flow-disable-line\n  var nameMap = oldNameMap || Object.create(null);\n\n  routes.forEach(function (route) {\n    addRouteRecord(pathList, pathMap, nameMap, route, parentRoute);\n  });\n\n  // ensure wildcard routes are always at the end\n  for (var i = 0, l = pathList.length; i < l; i++) {\n    if (pathList[i] === '*') {\n      pathList.push(pathList.splice(i, 1)[0]);\n      l--;\n      i--;\n    }\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // warn if routes do not include leading slashes\n    var found = pathList\n    // check for missing leading slash\n      .filter(function (path) { return path && path.charAt(0) !== '*' && path.charAt(0) !== '/'; });\n\n    if (found.length > 0) {\n      var pathNames = found.map(function (path) { return (\"- \" + path); }).join('\\n');\n      warn(false, (\"Non-nested routes must include a leading slash character. Fix the following routes: \\n\" + pathNames));\n    }\n  }\n\n  return {\n    pathList: pathList,\n    pathMap: pathMap,\n    nameMap: nameMap\n  }\n}\n\nfunction addRouteRecord (\n  pathList,\n  pathMap,\n  nameMap,\n  route,\n  parent,\n  matchAs\n) {\n  var path = route.path;\n  var name = route.name;\n  if (process.env.NODE_ENV !== 'production') {\n    assert(path != null, \"\\\"path\\\" is required in a route configuration.\");\n    assert(\n      typeof route.component !== 'string',\n      \"route config \\\"component\\\" for path: \" + (String(\n        path || name\n      )) + \" cannot be a \" + \"string id. Use an actual component instead.\"\n    );\n\n    warn(\n      // eslint-disable-next-line no-control-regex\n      !/[^\\u0000-\\u007F]+/.test(path),\n      \"Route with path \\\"\" + path + \"\\\" contains unencoded characters, make sure \" +\n        \"your path is correctly encoded before passing it to the router. Use \" +\n        \"encodeURI to encode static segments of your path.\"\n    );\n  }\n\n  var pathToRegexpOptions =\n    route.pathToRegexpOptions || {};\n  var normalizedPath = normalizePath(path, parent, pathToRegexpOptions.strict);\n\n  if (typeof route.caseSensitive === 'boolean') {\n    pathToRegexpOptions.sensitive = route.caseSensitive;\n  }\n\n  var record = {\n    path: normalizedPath,\n    regex: compileRouteRegex(normalizedPath, pathToRegexpOptions),\n    components: route.components || { default: route.component },\n    alias: route.alias\n      ? typeof route.alias === 'string'\n        ? [route.alias]\n        : route.alias\n      : [],\n    instances: {},\n    enteredCbs: {},\n    name: name,\n    parent: parent,\n    matchAs: matchAs,\n    redirect: route.redirect,\n    beforeEnter: route.beforeEnter,\n    meta: route.meta || {},\n    props:\n      route.props == null\n        ? {}\n        : route.components\n          ? route.props\n          : { default: route.props }\n  };\n\n  if (route.children) {\n    // Warn if route is named, does not redirect and has a default child route.\n    // If users navigate to this route by name, the default child will\n    // not be rendered (GH Issue #629)\n    if (process.env.NODE_ENV !== 'production') {\n      if (\n        route.name &&\n        !route.redirect &&\n        route.children.some(function (child) { return /^\\/?$/.test(child.path); })\n      ) {\n        warn(\n          false,\n          \"Named Route '\" + (route.name) + \"' has a default child route. \" +\n            \"When navigating to this named route (:to=\\\"{name: '\" + (route.name) + \"'}\\\"), \" +\n            \"the default child route will not be rendered. Remove the name from \" +\n            \"this route and use the name of the default child route for named \" +\n            \"links instead.\"\n        );\n      }\n    }\n    route.children.forEach(function (child) {\n      var childMatchAs = matchAs\n        ? cleanPath((matchAs + \"/\" + (child.path)))\n        : undefined;\n      addRouteRecord(pathList, pathMap, nameMap, child, record, childMatchAs);\n    });\n  }\n\n  if (!pathMap[record.path]) {\n    pathList.push(record.path);\n    pathMap[record.path] = record;\n  }\n\n  if (route.alias !== undefined) {\n    var aliases = Array.isArray(route.alias) ? route.alias : [route.alias];\n    for (var i = 0; i < aliases.length; ++i) {\n      var alias = aliases[i];\n      if (process.env.NODE_ENV !== 'production' && alias === path) {\n        warn(\n          false,\n          (\"Found an alias with the same value as the path: \\\"\" + path + \"\\\". You have to remove that alias. It will be ignored in development.\")\n        );\n        // skip in dev to make it work\n        continue\n      }\n\n      var aliasRoute = {\n        path: alias,\n        children: route.children\n      };\n      addRouteRecord(\n        pathList,\n        pathMap,\n        nameMap,\n        aliasRoute,\n        parent,\n        record.path || '/' // matchAs\n      );\n    }\n  }\n\n  if (name) {\n    if (!nameMap[name]) {\n      nameMap[name] = record;\n    } else if (process.env.NODE_ENV !== 'production' && !matchAs) {\n      warn(\n        false,\n        \"Duplicate named routes definition: \" +\n          \"{ name: \\\"\" + name + \"\\\", path: \\\"\" + (record.path) + \"\\\" }\"\n      );\n    }\n  }\n}\n\nfunction compileRouteRegex (\n  path,\n  pathToRegexpOptions\n) {\n  var regex = pathToRegexp_1(path, [], pathToRegexpOptions);\n  if (process.env.NODE_ENV !== 'production') {\n    var keys = Object.create(null);\n    regex.keys.forEach(function (key) {\n      warn(\n        !keys[key.name],\n        (\"Duplicate param keys in route with path: \\\"\" + path + \"\\\"\")\n      );\n      keys[key.name] = true;\n    });\n  }\n  return regex\n}\n\nfunction normalizePath (\n  path,\n  parent,\n  strict\n) {\n  if (!strict) { path = path.replace(/\\/$/, ''); }\n  if (path[0] === '/') { return path }\n  if (parent == null) { return path }\n  return cleanPath(((parent.path) + \"/\" + path))\n}\n\n/*  */\n\n\n\nfunction createMatcher (\n  routes,\n  router\n) {\n  var ref = createRouteMap(routes);\n  var pathList = ref.pathList;\n  var pathMap = ref.pathMap;\n  var nameMap = ref.nameMap;\n\n  function addRoutes (routes) {\n    createRouteMap(routes, pathList, pathMap, nameMap);\n  }\n\n  function addRoute (parentOrRoute, route) {\n    var parent = (typeof parentOrRoute !== 'object') ? nameMap[parentOrRoute] : undefined;\n    // $flow-disable-line\n    createRouteMap([route || parentOrRoute], pathList, pathMap, nameMap, parent);\n\n    // add aliases of parent\n    if (parent && parent.alias.length) {\n      createRouteMap(\n        // $flow-disable-line route is defined if parent is\n        parent.alias.map(function (alias) { return ({ path: alias, children: [route] }); }),\n        pathList,\n        pathMap,\n        nameMap,\n        parent\n      );\n    }\n  }\n\n  function getRoutes () {\n    return pathList.map(function (path) { return pathMap[path]; })\n  }\n\n  function match (\n    raw,\n    currentRoute,\n    redirectedFrom\n  ) {\n    var location = normalizeLocation(raw, currentRoute, false, router);\n    var name = location.name;\n\n    if (name) {\n      var record = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        warn(record, (\"Route with name '\" + name + \"' does not exist\"));\n      }\n      if (!record) { return _createRoute(null, location) }\n      var paramNames = record.regex.keys\n        .filter(function (key) { return !key.optional; })\n        .map(function (key) { return key.name; });\n\n      if (typeof location.params !== 'object') {\n        location.params = {};\n      }\n\n      if (currentRoute && typeof currentRoute.params === 'object') {\n        for (var key in currentRoute.params) {\n          if (!(key in location.params) && paramNames.indexOf(key) > -1) {\n            location.params[key] = currentRoute.params[key];\n          }\n        }\n      }\n\n      location.path = fillParams(record.path, location.params, (\"named route \\\"\" + name + \"\\\"\"));\n      return _createRoute(record, location, redirectedFrom)\n    } else if (location.path) {\n      location.params = {};\n      for (var i = 0; i < pathList.length; i++) {\n        var path = pathList[i];\n        var record$1 = pathMap[path];\n        if (matchRoute(record$1.regex, location.path, location.params)) {\n          return _createRoute(record$1, location, redirectedFrom)\n        }\n      }\n    }\n    // no match\n    return _createRoute(null, location)\n  }\n\n  function redirect (\n    record,\n    location\n  ) {\n    var originalRedirect = record.redirect;\n    var redirect = typeof originalRedirect === 'function'\n      ? originalRedirect(createRoute(record, location, null, router))\n      : originalRedirect;\n\n    if (typeof redirect === 'string') {\n      redirect = { path: redirect };\n    }\n\n    if (!redirect || typeof redirect !== 'object') {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(\n          false, (\"invalid redirect option: \" + (JSON.stringify(redirect)))\n        );\n      }\n      return _createRoute(null, location)\n    }\n\n    var re = redirect;\n    var name = re.name;\n    var path = re.path;\n    var query = location.query;\n    var hash = location.hash;\n    var params = location.params;\n    query = re.hasOwnProperty('query') ? re.query : query;\n    hash = re.hasOwnProperty('hash') ? re.hash : hash;\n    params = re.hasOwnProperty('params') ? re.params : params;\n\n    if (name) {\n      // resolved named direct\n      var targetRecord = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        assert(targetRecord, (\"redirect failed: named route \\\"\" + name + \"\\\" not found.\"));\n      }\n      return match({\n        _normalized: true,\n        name: name,\n        query: query,\n        hash: hash,\n        params: params\n      }, undefined, location)\n    } else if (path) {\n      // 1. resolve relative redirect\n      var rawPath = resolveRecordPath(path, record);\n      // 2. resolve params\n      var resolvedPath = fillParams(rawPath, params, (\"redirect route with path \\\"\" + rawPath + \"\\\"\"));\n      // 3. rematch with existing query and hash\n      return match({\n        _normalized: true,\n        path: resolvedPath,\n        query: query,\n        hash: hash\n      }, undefined, location)\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(false, (\"invalid redirect option: \" + (JSON.stringify(redirect))));\n      }\n      return _createRoute(null, location)\n    }\n  }\n\n  function alias (\n    record,\n    location,\n    matchAs\n  ) {\n    var aliasedPath = fillParams(matchAs, location.params, (\"aliased route with path \\\"\" + matchAs + \"\\\"\"));\n    var aliasedMatch = match({\n      _normalized: true,\n      path: aliasedPath\n    });\n    if (aliasedMatch) {\n      var matched = aliasedMatch.matched;\n      var aliasedRecord = matched[matched.length - 1];\n      location.params = aliasedMatch.params;\n      return _createRoute(aliasedRecord, location)\n    }\n    return _createRoute(null, location)\n  }\n\n  function _createRoute (\n    record,\n    location,\n    redirectedFrom\n  ) {\n    if (record && record.redirect) {\n      return redirect(record, redirectedFrom || location)\n    }\n    if (record && record.matchAs) {\n      return alias(record, location, record.matchAs)\n    }\n    return createRoute(record, location, redirectedFrom, router)\n  }\n\n  return {\n    match: match,\n    addRoute: addRoute,\n    getRoutes: getRoutes,\n    addRoutes: addRoutes\n  }\n}\n\nfunction matchRoute (\n  regex,\n  path,\n  params\n) {\n  var m = path.match(regex);\n\n  if (!m) {\n    return false\n  } else if (!params) {\n    return true\n  }\n\n  for (var i = 1, len = m.length; i < len; ++i) {\n    var key = regex.keys[i - 1];\n    if (key) {\n      // Fix #1994: using * with props: true generates a param named 0\n      params[key.name || 'pathMatch'] = typeof m[i] === 'string' ? decode(m[i]) : m[i];\n    }\n  }\n\n  return true\n}\n\nfunction resolveRecordPath (path, record) {\n  return resolvePath(path, record.parent ? record.parent.path : '/', true)\n}\n\n/*  */\n\n// use User Timing api (if present) for more accurate key precision\nvar Time =\n  inBrowser && window.performance && window.performance.now\n    ? window.performance\n    : Date;\n\nfunction genStateKey () {\n  return Time.now().toFixed(3)\n}\n\nvar _key = genStateKey();\n\nfunction getStateKey () {\n  return _key\n}\n\nfunction setStateKey (key) {\n  return (_key = key)\n}\n\n/*  */\n\nvar positionStore = Object.create(null);\n\nfunction setupScroll () {\n  // Prevent browser scroll behavior on History popstate\n  if ('scrollRestoration' in window.history) {\n    window.history.scrollRestoration = 'manual';\n  }\n  // Fix for #1585 for Firefox\n  // Fix for #2195 Add optional third attribute to workaround a bug in safari https://bugs.webkit.org/show_bug.cgi?id=182678\n  // Fix for #2774 Support for apps loaded from Windows file shares not mapped to network drives: replaced location.origin with\n  // window.location.protocol + '//' + window.location.host\n  // location.host contains the port and location.hostname doesn't\n  var protocolAndPath = window.location.protocol + '//' + window.location.host;\n  var absolutePath = window.location.href.replace(protocolAndPath, '');\n  // preserve existing history state as it could be overriden by the user\n  var stateCopy = extend({}, window.history.state);\n  stateCopy.key = getStateKey();\n  window.history.replaceState(stateCopy, '', absolutePath);\n  window.addEventListener('popstate', handlePopState);\n  return function () {\n    window.removeEventListener('popstate', handlePopState);\n  }\n}\n\nfunction handleScroll (\n  router,\n  to,\n  from,\n  isPop\n) {\n  if (!router.app) {\n    return\n  }\n\n  var behavior = router.options.scrollBehavior;\n  if (!behavior) {\n    return\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    assert(typeof behavior === 'function', \"scrollBehavior must be a function\");\n  }\n\n  // wait until re-render finishes before scrolling\n  router.app.$nextTick(function () {\n    var position = getScrollPosition();\n    var shouldScroll = behavior.call(\n      router,\n      to,\n      from,\n      isPop ? position : null\n    );\n\n    if (!shouldScroll) {\n      return\n    }\n\n    if (typeof shouldScroll.then === 'function') {\n      shouldScroll\n        .then(function (shouldScroll) {\n          scrollToPosition((shouldScroll), position);\n        })\n        .catch(function (err) {\n          if (process.env.NODE_ENV !== 'production') {\n            assert(false, err.toString());\n          }\n        });\n    } else {\n      scrollToPosition(shouldScroll, position);\n    }\n  });\n}\n\nfunction saveScrollPosition () {\n  var key = getStateKey();\n  if (key) {\n    positionStore[key] = {\n      x: window.pageXOffset,\n      y: window.pageYOffset\n    };\n  }\n}\n\nfunction handlePopState (e) {\n  saveScrollPosition();\n  if (e.state && e.state.key) {\n    setStateKey(e.state.key);\n  }\n}\n\nfunction getScrollPosition () {\n  var key = getStateKey();\n  if (key) {\n    return positionStore[key]\n  }\n}\n\nfunction getElementPosition (el, offset) {\n  var docEl = document.documentElement;\n  var docRect = docEl.getBoundingClientRect();\n  var elRect = el.getBoundingClientRect();\n  return {\n    x: elRect.left - docRect.left - offset.x,\n    y: elRect.top - docRect.top - offset.y\n  }\n}\n\nfunction isValidPosition (obj) {\n  return isNumber(obj.x) || isNumber(obj.y)\n}\n\nfunction normalizePosition (obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : window.pageXOffset,\n    y: isNumber(obj.y) ? obj.y : window.pageYOffset\n  }\n}\n\nfunction normalizeOffset (obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : 0,\n    y: isNumber(obj.y) ? obj.y : 0\n  }\n}\n\nfunction isNumber (v) {\n  return typeof v === 'number'\n}\n\nvar hashStartsWithNumberRE = /^#\\d/;\n\nfunction scrollToPosition (shouldScroll, position) {\n  var isObject = typeof shouldScroll === 'object';\n  if (isObject && typeof shouldScroll.selector === 'string') {\n    // getElementById would still fail if the selector contains a more complicated query like #main[data-attr]\n    // but at the same time, it doesn't make much sense to select an element with an id and an extra selector\n    var el = hashStartsWithNumberRE.test(shouldScroll.selector) // $flow-disable-line\n      ? document.getElementById(shouldScroll.selector.slice(1)) // $flow-disable-line\n      : document.querySelector(shouldScroll.selector);\n\n    if (el) {\n      var offset =\n        shouldScroll.offset && typeof shouldScroll.offset === 'object'\n          ? shouldScroll.offset\n          : {};\n      offset = normalizeOffset(offset);\n      position = getElementPosition(el, offset);\n    } else if (isValidPosition(shouldScroll)) {\n      position = normalizePosition(shouldScroll);\n    }\n  } else if (isObject && isValidPosition(shouldScroll)) {\n    position = normalizePosition(shouldScroll);\n  }\n\n  if (position) {\n    // $flow-disable-line\n    if ('scrollBehavior' in document.documentElement.style) {\n      window.scrollTo({\n        left: position.x,\n        top: position.y,\n        // $flow-disable-line\n        behavior: shouldScroll.behavior\n      });\n    } else {\n      window.scrollTo(position.x, position.y);\n    }\n  }\n}\n\n/*  */\n\nvar supportsPushState =\n  inBrowser &&\n  (function () {\n    var ua = window.navigator.userAgent;\n\n    if (\n      (ua.indexOf('Android 2.') !== -1 || ua.indexOf('Android 4.0') !== -1) &&\n      ua.indexOf('Mobile Safari') !== -1 &&\n      ua.indexOf('Chrome') === -1 &&\n      ua.indexOf('Windows Phone') === -1\n    ) {\n      return false\n    }\n\n    return window.history && typeof window.history.pushState === 'function'\n  })();\n\nfunction pushState (url, replace) {\n  saveScrollPosition();\n  // try...catch the pushState call to get around Safari\n  // DOM Exception 18 where it limits to 100 pushState calls\n  var history = window.history;\n  try {\n    if (replace) {\n      // preserve existing history state as it could be overriden by the user\n      var stateCopy = extend({}, history.state);\n      stateCopy.key = getStateKey();\n      history.replaceState(stateCopy, '', url);\n    } else {\n      history.pushState({ key: setStateKey(genStateKey()) }, '', url);\n    }\n  } catch (e) {\n    window.location[replace ? 'replace' : 'assign'](url);\n  }\n}\n\nfunction replaceState (url) {\n  pushState(url, true);\n}\n\n// When changing thing, also edit router.d.ts\nvar NavigationFailureType = {\n  redirected: 2,\n  aborted: 4,\n  cancelled: 8,\n  duplicated: 16\n};\n\nfunction createNavigationRedirectedError (from, to) {\n  return createRouterError(\n    from,\n    to,\n    NavigationFailureType.redirected,\n    (\"Redirected when going from \\\"\" + (from.fullPath) + \"\\\" to \\\"\" + (stringifyRoute(\n      to\n    )) + \"\\\" via a navigation guard.\")\n  )\n}\n\nfunction createNavigationDuplicatedError (from, to) {\n  var error = createRouterError(\n    from,\n    to,\n    NavigationFailureType.duplicated,\n    (\"Avoided redundant navigation to current location: \\\"\" + (from.fullPath) + \"\\\".\")\n  );\n  // backwards compatible with the first introduction of Errors\n  error.name = 'NavigationDuplicated';\n  return error\n}\n\nfunction createNavigationCancelledError (from, to) {\n  return createRouterError(\n    from,\n    to,\n    NavigationFailureType.cancelled,\n    (\"Navigation cancelled from \\\"\" + (from.fullPath) + \"\\\" to \\\"\" + (to.fullPath) + \"\\\" with a new navigation.\")\n  )\n}\n\nfunction createNavigationAbortedError (from, to) {\n  return createRouterError(\n    from,\n    to,\n    NavigationFailureType.aborted,\n    (\"Navigation aborted from \\\"\" + (from.fullPath) + \"\\\" to \\\"\" + (to.fullPath) + \"\\\" via a navigation guard.\")\n  )\n}\n\nfunction createRouterError (from, to, type, message) {\n  var error = new Error(message);\n  error._isRouter = true;\n  error.from = from;\n  error.to = to;\n  error.type = type;\n\n  return error\n}\n\nvar propertiesToLog = ['params', 'query', 'hash'];\n\nfunction stringifyRoute (to) {\n  if (typeof to === 'string') { return to }\n  if ('path' in to) { return to.path }\n  var location = {};\n  propertiesToLog.forEach(function (key) {\n    if (key in to) { location[key] = to[key]; }\n  });\n  return JSON.stringify(location, null, 2)\n}\n\nfunction isError (err) {\n  return Object.prototype.toString.call(err).indexOf('Error') > -1\n}\n\nfunction isNavigationFailure (err, errorType) {\n  return (\n    isError(err) &&\n    err._isRouter &&\n    (errorType == null || err.type === errorType)\n  )\n}\n\n/*  */\n\nfunction runQueue (queue, fn, cb) {\n  var step = function (index) {\n    if (index >= queue.length) {\n      cb();\n    } else {\n      if (queue[index]) {\n        fn(queue[index], function () {\n          step(index + 1);\n        });\n      } else {\n        step(index + 1);\n      }\n    }\n  };\n  step(0);\n}\n\n/*  */\n\nfunction resolveAsyncComponents (matched) {\n  return function (to, from, next) {\n    var hasAsync = false;\n    var pending = 0;\n    var error = null;\n\n    flatMapComponents(matched, function (def, _, match, key) {\n      // if it's a function and doesn't have cid attached,\n      // assume it's an async component resolve function.\n      // we are not using Vue's default async resolving mechanism because\n      // we want to halt the navigation until the incoming component has been\n      // resolved.\n      if (typeof def === 'function' && def.cid === undefined) {\n        hasAsync = true;\n        pending++;\n\n        var resolve = once(function (resolvedDef) {\n          if (isESModule(resolvedDef)) {\n            resolvedDef = resolvedDef.default;\n          }\n          // save resolved on async factory in case it's used elsewhere\n          def.resolved = typeof resolvedDef === 'function'\n            ? resolvedDef\n            : _Vue.extend(resolvedDef);\n          match.components[key] = resolvedDef;\n          pending--;\n          if (pending <= 0) {\n            next();\n          }\n        });\n\n        var reject = once(function (reason) {\n          var msg = \"Failed to resolve async component \" + key + \": \" + reason;\n          process.env.NODE_ENV !== 'production' && warn(false, msg);\n          if (!error) {\n            error = isError(reason)\n              ? reason\n              : new Error(msg);\n            next(error);\n          }\n        });\n\n        var res;\n        try {\n          res = def(resolve, reject);\n        } catch (e) {\n          reject(e);\n        }\n        if (res) {\n          if (typeof res.then === 'function') {\n            res.then(resolve, reject);\n          } else {\n            // new syntax in Vue 2.3\n            var comp = res.component;\n            if (comp && typeof comp.then === 'function') {\n              comp.then(resolve, reject);\n            }\n          }\n        }\n      }\n    });\n\n    if (!hasAsync) { next(); }\n  }\n}\n\nfunction flatMapComponents (\n  matched,\n  fn\n) {\n  return flatten(matched.map(function (m) {\n    return Object.keys(m.components).map(function (key) { return fn(\n      m.components[key],\n      m.instances[key],\n      m, key\n    ); })\n  }))\n}\n\nfunction flatten (arr) {\n  return Array.prototype.concat.apply([], arr)\n}\n\nvar hasSymbol =\n  typeof Symbol === 'function' &&\n  typeof Symbol.toStringTag === 'symbol';\n\nfunction isESModule (obj) {\n  return obj.__esModule || (hasSymbol && obj[Symbol.toStringTag] === 'Module')\n}\n\n// in Webpack 2, require.ensure now also returns a Promise\n// so the resolve/reject functions may get called an extra time\n// if the user uses an arrow function shorthand that happens to\n// return that Promise.\nfunction once (fn) {\n  var called = false;\n  return function () {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    if (called) { return }\n    called = true;\n    return fn.apply(this, args)\n  }\n}\n\n/*  */\n\nvar History = function History (router, base) {\n  this.router = router;\n  this.base = normalizeBase(base);\n  // start with a route object that stands for \"nowhere\"\n  this.current = START;\n  this.pending = null;\n  this.ready = false;\n  this.readyCbs = [];\n  this.readyErrorCbs = [];\n  this.errorCbs = [];\n  this.listeners = [];\n};\n\nHistory.prototype.listen = function listen (cb) {\n  this.cb = cb;\n};\n\nHistory.prototype.onReady = function onReady (cb, errorCb) {\n  if (this.ready) {\n    cb();\n  } else {\n    this.readyCbs.push(cb);\n    if (errorCb) {\n      this.readyErrorCbs.push(errorCb);\n    }\n  }\n};\n\nHistory.prototype.onError = function onError (errorCb) {\n  this.errorCbs.push(errorCb);\n};\n\nHistory.prototype.transitionTo = function transitionTo (\n  location,\n  onComplete,\n  onAbort\n) {\n    var this$1$1 = this;\n\n  var route;\n  // catch redirect option https://github.com/vuejs/vue-router/issues/3201\n  try {\n    route = this.router.match(location, this.current);\n  } catch (e) {\n    this.errorCbs.forEach(function (cb) {\n      cb(e);\n    });\n    // Exception should still be thrown\n    throw e\n  }\n  var prev = this.current;\n  this.confirmTransition(\n    route,\n    function () {\n      this$1$1.updateRoute(route);\n      onComplete && onComplete(route);\n      this$1$1.ensureURL();\n      this$1$1.router.afterHooks.forEach(function (hook) {\n        hook && hook(route, prev);\n      });\n\n      // fire ready cbs once\n      if (!this$1$1.ready) {\n        this$1$1.ready = true;\n        this$1$1.readyCbs.forEach(function (cb) {\n          cb(route);\n        });\n      }\n    },\n    function (err) {\n      if (onAbort) {\n        onAbort(err);\n      }\n      if (err && !this$1$1.ready) {\n        // Initial redirection should not mark the history as ready yet\n        // because it's triggered by the redirection instead\n        // https://github.com/vuejs/vue-router/issues/3225\n        // https://github.com/vuejs/vue-router/issues/3331\n        if (!isNavigationFailure(err, NavigationFailureType.redirected) || prev !== START) {\n          this$1$1.ready = true;\n          this$1$1.readyErrorCbs.forEach(function (cb) {\n            cb(err);\n          });\n        }\n      }\n    }\n  );\n};\n\nHistory.prototype.confirmTransition = function confirmTransition (route, onComplete, onAbort) {\n    var this$1$1 = this;\n\n  var current = this.current;\n  this.pending = route;\n  var abort = function (err) {\n    // changed after adding errors with\n    // https://github.com/vuejs/vue-router/pull/3047 before that change,\n    // redirect and aborted navigation would produce an err == null\n    if (!isNavigationFailure(err) && isError(err)) {\n      if (this$1$1.errorCbs.length) {\n        this$1$1.errorCbs.forEach(function (cb) {\n          cb(err);\n        });\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(false, 'uncaught error during route navigation:');\n        }\n        console.error(err);\n      }\n    }\n    onAbort && onAbort(err);\n  };\n  var lastRouteIndex = route.matched.length - 1;\n  var lastCurrentIndex = current.matched.length - 1;\n  if (\n    isSameRoute(route, current) &&\n    // in the case the route map has been dynamically appended to\n    lastRouteIndex === lastCurrentIndex &&\n    route.matched[lastRouteIndex] === current.matched[lastCurrentIndex]\n  ) {\n    this.ensureURL();\n    if (route.hash) {\n      handleScroll(this.router, current, route, false);\n    }\n    return abort(createNavigationDuplicatedError(current, route))\n  }\n\n  var ref = resolveQueue(\n    this.current.matched,\n    route.matched\n  );\n    var updated = ref.updated;\n    var deactivated = ref.deactivated;\n    var activated = ref.activated;\n\n  var queue = [].concat(\n    // in-component leave guards\n    extractLeaveGuards(deactivated),\n    // global before hooks\n    this.router.beforeHooks,\n    // in-component update hooks\n    extractUpdateHooks(updated),\n    // in-config enter guards\n    activated.map(function (m) { return m.beforeEnter; }),\n    // async components\n    resolveAsyncComponents(activated)\n  );\n\n  var iterator = function (hook, next) {\n    if (this$1$1.pending !== route) {\n      return abort(createNavigationCancelledError(current, route))\n    }\n    try {\n      hook(route, current, function (to) {\n        if (to === false) {\n          // next(false) -> abort navigation, ensure current URL\n          this$1$1.ensureURL(true);\n          abort(createNavigationAbortedError(current, route));\n        } else if (isError(to)) {\n          this$1$1.ensureURL(true);\n          abort(to);\n        } else if (\n          typeof to === 'string' ||\n          (typeof to === 'object' &&\n            (typeof to.path === 'string' || typeof to.name === 'string'))\n        ) {\n          // next('/') or next({ path: '/' }) -> redirect\n          abort(createNavigationRedirectedError(current, route));\n          if (typeof to === 'object' && to.replace) {\n            this$1$1.replace(to);\n          } else {\n            this$1$1.push(to);\n          }\n        } else {\n          // confirm transition and pass on the value\n          next(to);\n        }\n      });\n    } catch (e) {\n      abort(e);\n    }\n  };\n\n  runQueue(queue, iterator, function () {\n    // wait until async components are resolved before\n    // extracting in-component enter guards\n    var enterGuards = extractEnterGuards(activated);\n    var queue = enterGuards.concat(this$1$1.router.resolveHooks);\n    runQueue(queue, iterator, function () {\n      if (this$1$1.pending !== route) {\n        return abort(createNavigationCancelledError(current, route))\n      }\n      this$1$1.pending = null;\n      onComplete(route);\n      if (this$1$1.router.app) {\n        this$1$1.router.app.$nextTick(function () {\n          handleRouteEntered(route);\n        });\n      }\n    });\n  });\n};\n\nHistory.prototype.updateRoute = function updateRoute (route) {\n  this.current = route;\n  this.cb && this.cb(route);\n};\n\nHistory.prototype.setupListeners = function setupListeners () {\n  // Default implementation is empty\n};\n\nHistory.prototype.teardown = function teardown () {\n  // clean up event listeners\n  // https://github.com/vuejs/vue-router/issues/2341\n  this.listeners.forEach(function (cleanupListener) {\n    cleanupListener();\n  });\n  this.listeners = [];\n\n  // reset current history route\n  // https://github.com/vuejs/vue-router/issues/3294\n  this.current = START;\n  this.pending = null;\n};\n\nfunction normalizeBase (base) {\n  if (!base) {\n    if (inBrowser) {\n      // respect <base> tag\n      var baseEl = document.querySelector('base');\n      base = (baseEl && baseEl.getAttribute('href')) || '/';\n      // strip full URL origin\n      base = base.replace(/^https?:\\/\\/[^\\/]+/, '');\n    } else {\n      base = '/';\n    }\n  }\n  // make sure there's the starting slash\n  if (base.charAt(0) !== '/') {\n    base = '/' + base;\n  }\n  // remove trailing slash\n  return base.replace(/\\/$/, '')\n}\n\nfunction resolveQueue (\n  current,\n  next\n) {\n  var i;\n  var max = Math.max(current.length, next.length);\n  for (i = 0; i < max; i++) {\n    if (current[i] !== next[i]) {\n      break\n    }\n  }\n  return {\n    updated: next.slice(0, i),\n    activated: next.slice(i),\n    deactivated: current.slice(i)\n  }\n}\n\nfunction extractGuards (\n  records,\n  name,\n  bind,\n  reverse\n) {\n  var guards = flatMapComponents(records, function (def, instance, match, key) {\n    var guard = extractGuard(def, name);\n    if (guard) {\n      return Array.isArray(guard)\n        ? guard.map(function (guard) { return bind(guard, instance, match, key); })\n        : bind(guard, instance, match, key)\n    }\n  });\n  return flatten(reverse ? guards.reverse() : guards)\n}\n\nfunction extractGuard (\n  def,\n  key\n) {\n  if (typeof def !== 'function') {\n    // extend now so that global mixins are applied.\n    def = _Vue.extend(def);\n  }\n  return def.options[key]\n}\n\nfunction extractLeaveGuards (deactivated) {\n  return extractGuards(deactivated, 'beforeRouteLeave', bindGuard, true)\n}\n\nfunction extractUpdateHooks (updated) {\n  return extractGuards(updated, 'beforeRouteUpdate', bindGuard)\n}\n\nfunction bindGuard (guard, instance) {\n  if (instance) {\n    return function boundRouteGuard () {\n      return guard.apply(instance, arguments)\n    }\n  }\n}\n\nfunction extractEnterGuards (\n  activated\n) {\n  return extractGuards(\n    activated,\n    'beforeRouteEnter',\n    function (guard, _, match, key) {\n      return bindEnterGuard(guard, match, key)\n    }\n  )\n}\n\nfunction bindEnterGuard (\n  guard,\n  match,\n  key\n) {\n  return function routeEnterGuard (to, from, next) {\n    return guard(to, from, function (cb) {\n      if (typeof cb === 'function') {\n        if (!match.enteredCbs[key]) {\n          match.enteredCbs[key] = [];\n        }\n        match.enteredCbs[key].push(cb);\n      }\n      next(cb);\n    })\n  }\n}\n\n/*  */\n\nvar HTML5History = /*@__PURE__*/(function (History) {\n  function HTML5History (router, base) {\n    History.call(this, router, base);\n\n    this._startLocation = getLocation(this.base);\n  }\n\n  if ( History ) HTML5History.__proto__ = History;\n  HTML5History.prototype = Object.create( History && History.prototype );\n  HTML5History.prototype.constructor = HTML5History;\n\n  HTML5History.prototype.setupListeners = function setupListeners () {\n    var this$1$1 = this;\n\n    if (this.listeners.length > 0) {\n      return\n    }\n\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n\n    var handleRoutingEvent = function () {\n      var current = this$1$1.current;\n\n      // Avoiding first `popstate` event dispatched in some browsers but first\n      // history route not updated since async guard at the same time.\n      var location = getLocation(this$1$1.base);\n      if (this$1$1.current === START && location === this$1$1._startLocation) {\n        return\n      }\n\n      this$1$1.transitionTo(location, function (route) {\n        if (supportsScroll) {\n          handleScroll(router, route, current, true);\n        }\n      });\n    };\n    window.addEventListener('popstate', handleRoutingEvent);\n    this.listeners.push(function () {\n      window.removeEventListener('popstate', handleRoutingEvent);\n    });\n  };\n\n  HTML5History.prototype.go = function go (n) {\n    window.history.go(n);\n  };\n\n  HTML5History.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      pushState(cleanPath(this$1$1.base + route.fullPath));\n      handleScroll(this$1$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HTML5History.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      replaceState(cleanPath(this$1$1.base + route.fullPath));\n      handleScroll(this$1$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HTML5History.prototype.ensureURL = function ensureURL (push) {\n    if (getLocation(this.base) !== this.current.fullPath) {\n      var current = cleanPath(this.base + this.current.fullPath);\n      push ? pushState(current) : replaceState(current);\n    }\n  };\n\n  HTML5History.prototype.getCurrentLocation = function getCurrentLocation () {\n    return getLocation(this.base)\n  };\n\n  return HTML5History;\n}(History));\n\nfunction getLocation (base) {\n  var path = window.location.pathname;\n  var pathLowerCase = path.toLowerCase();\n  var baseLowerCase = base.toLowerCase();\n  // base=\"/a\" shouldn't turn path=\"/app\" into \"/a/pp\"\n  // https://github.com/vuejs/vue-router/issues/3555\n  // so we ensure the trailing slash in the base\n  if (base && ((pathLowerCase === baseLowerCase) ||\n    (pathLowerCase.indexOf(cleanPath(baseLowerCase + '/')) === 0))) {\n    path = path.slice(base.length);\n  }\n  return (path || '/') + window.location.search + window.location.hash\n}\n\n/*  */\n\nvar HashHistory = /*@__PURE__*/(function (History) {\n  function HashHistory (router, base, fallback) {\n    History.call(this, router, base);\n    // check history fallback deeplinking\n    if (fallback && checkFallback(this.base)) {\n      return\n    }\n    ensureSlash();\n  }\n\n  if ( History ) HashHistory.__proto__ = History;\n  HashHistory.prototype = Object.create( History && History.prototype );\n  HashHistory.prototype.constructor = HashHistory;\n\n  // this is delayed until the app mounts\n  // to avoid the hashchange listener being fired too early\n  HashHistory.prototype.setupListeners = function setupListeners () {\n    var this$1$1 = this;\n\n    if (this.listeners.length > 0) {\n      return\n    }\n\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n\n    var handleRoutingEvent = function () {\n      var current = this$1$1.current;\n      if (!ensureSlash()) {\n        return\n      }\n      this$1$1.transitionTo(getHash(), function (route) {\n        if (supportsScroll) {\n          handleScroll(this$1$1.router, route, current, true);\n        }\n        if (!supportsPushState) {\n          replaceHash(route.fullPath);\n        }\n      });\n    };\n    var eventType = supportsPushState ? 'popstate' : 'hashchange';\n    window.addEventListener(\n      eventType,\n      handleRoutingEvent\n    );\n    this.listeners.push(function () {\n      window.removeEventListener(eventType, handleRoutingEvent);\n    });\n  };\n\n  HashHistory.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(\n      location,\n      function (route) {\n        pushHash(route.fullPath);\n        handleScroll(this$1$1.router, route, fromRoute, false);\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  HashHistory.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(\n      location,\n      function (route) {\n        replaceHash(route.fullPath);\n        handleScroll(this$1$1.router, route, fromRoute, false);\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  HashHistory.prototype.go = function go (n) {\n    window.history.go(n);\n  };\n\n  HashHistory.prototype.ensureURL = function ensureURL (push) {\n    var current = this.current.fullPath;\n    if (getHash() !== current) {\n      push ? pushHash(current) : replaceHash(current);\n    }\n  };\n\n  HashHistory.prototype.getCurrentLocation = function getCurrentLocation () {\n    return getHash()\n  };\n\n  return HashHistory;\n}(History));\n\nfunction checkFallback (base) {\n  var location = getLocation(base);\n  if (!/^\\/#/.test(location)) {\n    window.location.replace(cleanPath(base + '/#' + location));\n    return true\n  }\n}\n\nfunction ensureSlash () {\n  var path = getHash();\n  if (path.charAt(0) === '/') {\n    return true\n  }\n  replaceHash('/' + path);\n  return false\n}\n\nfunction getHash () {\n  // We can't use window.location.hash here because it's not\n  // consistent across browsers - Firefox will pre-decode it!\n  var href = window.location.href;\n  var index = href.indexOf('#');\n  // empty path\n  if (index < 0) { return '' }\n\n  href = href.slice(index + 1);\n\n  return href\n}\n\nfunction getUrl (path) {\n  var href = window.location.href;\n  var i = href.indexOf('#');\n  var base = i >= 0 ? href.slice(0, i) : href;\n  return (base + \"#\" + path)\n}\n\nfunction pushHash (path) {\n  if (supportsPushState) {\n    pushState(getUrl(path));\n  } else {\n    window.location.hash = path;\n  }\n}\n\nfunction replaceHash (path) {\n  if (supportsPushState) {\n    replaceState(getUrl(path));\n  } else {\n    window.location.replace(getUrl(path));\n  }\n}\n\n/*  */\n\nvar AbstractHistory = /*@__PURE__*/(function (History) {\n  function AbstractHistory (router, base) {\n    History.call(this, router, base);\n    this.stack = [];\n    this.index = -1;\n  }\n\n  if ( History ) AbstractHistory.__proto__ = History;\n  AbstractHistory.prototype = Object.create( History && History.prototype );\n  AbstractHistory.prototype.constructor = AbstractHistory;\n\n  AbstractHistory.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    this.transitionTo(\n      location,\n      function (route) {\n        this$1$1.stack = this$1$1.stack.slice(0, this$1$1.index + 1).concat(route);\n        this$1$1.index++;\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  AbstractHistory.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n    this.transitionTo(\n      location,\n      function (route) {\n        this$1$1.stack = this$1$1.stack.slice(0, this$1$1.index).concat(route);\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  AbstractHistory.prototype.go = function go (n) {\n    var this$1$1 = this;\n\n    var targetIndex = this.index + n;\n    if (targetIndex < 0 || targetIndex >= this.stack.length) {\n      return\n    }\n    var route = this.stack[targetIndex];\n    this.confirmTransition(\n      route,\n      function () {\n        var prev = this$1$1.current;\n        this$1$1.index = targetIndex;\n        this$1$1.updateRoute(route);\n        this$1$1.router.afterHooks.forEach(function (hook) {\n          hook && hook(route, prev);\n        });\n      },\n      function (err) {\n        if (isNavigationFailure(err, NavigationFailureType.duplicated)) {\n          this$1$1.index = targetIndex;\n        }\n      }\n    );\n  };\n\n  AbstractHistory.prototype.getCurrentLocation = function getCurrentLocation () {\n    var current = this.stack[this.stack.length - 1];\n    return current ? current.fullPath : '/'\n  };\n\n  AbstractHistory.prototype.ensureURL = function ensureURL () {\n    // noop\n  };\n\n  return AbstractHistory;\n}(History));\n\n/*  */\n\n\n\nvar VueRouter = function VueRouter (options) {\n  if ( options === void 0 ) options = {};\n\n  if (process.env.NODE_ENV !== 'production') {\n    warn(this instanceof VueRouter, \"Router must be called with the new operator.\");\n  }\n  this.app = null;\n  this.apps = [];\n  this.options = options;\n  this.beforeHooks = [];\n  this.resolveHooks = [];\n  this.afterHooks = [];\n  this.matcher = createMatcher(options.routes || [], this);\n\n  var mode = options.mode || 'hash';\n  this.fallback =\n    mode === 'history' && !supportsPushState && options.fallback !== false;\n  if (this.fallback) {\n    mode = 'hash';\n  }\n  if (!inBrowser) {\n    mode = 'abstract';\n  }\n  this.mode = mode;\n\n  switch (mode) {\n    case 'history':\n      this.history = new HTML5History(this, options.base);\n      break\n    case 'hash':\n      this.history = new HashHistory(this, options.base, this.fallback);\n      break\n    case 'abstract':\n      this.history = new AbstractHistory(this, options.base);\n      break\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        assert(false, (\"invalid mode: \" + mode));\n      }\n  }\n};\n\nvar prototypeAccessors = { currentRoute: { configurable: true } };\n\nVueRouter.prototype.match = function match (raw, current, redirectedFrom) {\n  return this.matcher.match(raw, current, redirectedFrom)\n};\n\nprototypeAccessors.currentRoute.get = function () {\n  return this.history && this.history.current\n};\n\nVueRouter.prototype.init = function init (app /* Vue component instance */) {\n    var this$1$1 = this;\n\n  process.env.NODE_ENV !== 'production' &&\n    assert(\n      install.installed,\n      \"not installed. Make sure to call `Vue.use(VueRouter)` \" +\n        \"before creating root instance.\"\n    );\n\n  this.apps.push(app);\n\n  // set up app destroyed handler\n  // https://github.com/vuejs/vue-router/issues/2639\n  app.$once('hook:destroyed', function () {\n    // clean out app from this.apps array once destroyed\n    var index = this$1$1.apps.indexOf(app);\n    if (index > -1) { this$1$1.apps.splice(index, 1); }\n    // ensure we still have a main app or null if no apps\n    // we do not release the router so it can be reused\n    if (this$1$1.app === app) { this$1$1.app = this$1$1.apps[0] || null; }\n\n    if (!this$1$1.app) { this$1$1.history.teardown(); }\n  });\n\n  // main app previously initialized\n  // return as we don't need to set up new history listener\n  if (this.app) {\n    return\n  }\n\n  this.app = app;\n\n  var history = this.history;\n\n  if (history instanceof HTML5History || history instanceof HashHistory) {\n    var handleInitialScroll = function (routeOrError) {\n      var from = history.current;\n      var expectScroll = this$1$1.options.scrollBehavior;\n      var supportsScroll = supportsPushState && expectScroll;\n\n      if (supportsScroll && 'fullPath' in routeOrError) {\n        handleScroll(this$1$1, routeOrError, from, false);\n      }\n    };\n    var setupListeners = function (routeOrError) {\n      history.setupListeners();\n      handleInitialScroll(routeOrError);\n    };\n    history.transitionTo(\n      history.getCurrentLocation(),\n      setupListeners,\n      setupListeners\n    );\n  }\n\n  history.listen(function (route) {\n    this$1$1.apps.forEach(function (app) {\n      app._route = route;\n    });\n  });\n};\n\nVueRouter.prototype.beforeEach = function beforeEach (fn) {\n  return registerHook(this.beforeHooks, fn)\n};\n\nVueRouter.prototype.beforeResolve = function beforeResolve (fn) {\n  return registerHook(this.resolveHooks, fn)\n};\n\nVueRouter.prototype.afterEach = function afterEach (fn) {\n  return registerHook(this.afterHooks, fn)\n};\n\nVueRouter.prototype.onReady = function onReady (cb, errorCb) {\n  this.history.onReady(cb, errorCb);\n};\n\nVueRouter.prototype.onError = function onError (errorCb) {\n  this.history.onError(errorCb);\n};\n\nVueRouter.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1$1.history.push(location, resolve, reject);\n    })\n  } else {\n    this.history.push(location, onComplete, onAbort);\n  }\n};\n\nVueRouter.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1$1.history.replace(location, resolve, reject);\n    })\n  } else {\n    this.history.replace(location, onComplete, onAbort);\n  }\n};\n\nVueRouter.prototype.go = function go (n) {\n  this.history.go(n);\n};\n\nVueRouter.prototype.back = function back () {\n  this.go(-1);\n};\n\nVueRouter.prototype.forward = function forward () {\n  this.go(1);\n};\n\nVueRouter.prototype.getMatchedComponents = function getMatchedComponents (to) {\n  var route = to\n    ? to.matched\n      ? to\n      : this.resolve(to).route\n    : this.currentRoute;\n  if (!route) {\n    return []\n  }\n  return [].concat.apply(\n    [],\n    route.matched.map(function (m) {\n      return Object.keys(m.components).map(function (key) {\n        return m.components[key]\n      })\n    })\n  )\n};\n\nVueRouter.prototype.resolve = function resolve (\n  to,\n  current,\n  append\n) {\n  current = current || this.history.current;\n  var location = normalizeLocation(to, current, append, this);\n  var route = this.match(location, current);\n  var fullPath = route.redirectedFrom || route.fullPath;\n  var base = this.history.base;\n  var href = createHref(base, fullPath, this.mode);\n  return {\n    location: location,\n    route: route,\n    href: href,\n    // for backwards compat\n    normalizedTo: location,\n    resolved: route\n  }\n};\n\nVueRouter.prototype.getRoutes = function getRoutes () {\n  return this.matcher.getRoutes()\n};\n\nVueRouter.prototype.addRoute = function addRoute (parentOrRoute, route) {\n  this.matcher.addRoute(parentOrRoute, route);\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\n\nVueRouter.prototype.addRoutes = function addRoutes (routes) {\n  if (process.env.NODE_ENV !== 'production') {\n    warn(false, 'router.addRoutes() is deprecated and has been removed in Vue Router 4. Use router.addRoute() instead.');\n  }\n  this.matcher.addRoutes(routes);\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\n\nObject.defineProperties( VueRouter.prototype, prototypeAccessors );\n\nvar VueRouter$1 = VueRouter;\n\nfunction registerHook (list, fn) {\n  list.push(fn);\n  return function () {\n    var i = list.indexOf(fn);\n    if (i > -1) { list.splice(i, 1); }\n  }\n}\n\nfunction createHref (base, fullPath, mode) {\n  var path = mode === 'hash' ? '#' + fullPath : fullPath;\n  return base ? cleanPath(base + '/' + path) : path\n}\n\n// We cannot remove this as it would be a breaking change\nVueRouter.install = install;\nVueRouter.version = '3.6.5';\nVueRouter.isNavigationFailure = isNavigationFailure;\nVueRouter.NavigationFailureType = NavigationFailureType;\nVueRouter.START_LOCATION = START;\n\nif (inBrowser && window.Vue) {\n  window.Vue.use(VueRouter);\n}\n\nvar version = '3.6.5';\n\nexport { NavigationFailureType, Link as RouterLink, View as RouterView, START as START_LOCATION, VueRouter$1 as default, isNavigationFailure, version };\n"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAAEC,SAAS,EAAEC,OAAO,EAAE;EACnC,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAIE,KAAK,CAAE,eAAe,GAAGD,OAAQ,CAAC;EAC9C;AACF;AAEA,SAASE,IAAIA,CAAEH,SAAS,EAAEC,OAAO,EAAE;EACjC,IAAI,CAACD,SAAS,EAAE;IACd,OAAOI,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACD,IAAI,CAAE,eAAe,GAAGF,OAAQ,CAAC;EAC7E;AACF;AAEA,SAASI,MAAMA,CAAEC,CAAC,EAAEC,CAAC,EAAE;EACrB,KAAK,IAAIC,GAAG,IAAID,CAAC,EAAE;IACjBD,CAAC,CAACE,GAAG,CAAC,GAAGD,CAAC,CAACC,GAAG,CAAC;EACjB;EACA,OAAOF,CAAC;AACV;;AAEA;;AAEA,IAAIG,eAAe,GAAG,UAAU;AAChC,IAAIC,qBAAqB,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAAE,OAAO,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;AAAE,CAAC;AACvF,IAAIC,OAAO,GAAG,MAAM;;AAEpB;AACA;AACA;AACA,IAAIC,MAAM,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,CACvDE,OAAO,CAACT,eAAe,EAAEC,qBAAqB,CAAC,CAC/CQ,OAAO,CAACJ,OAAO,EAAE,GAAG,CAAC;AAAE,CAAC;AAE7B,SAASK,MAAMA,CAAEH,GAAG,EAAE;EACpB,IAAI;IACF,OAAOI,kBAAkB,CAACJ,GAAG,CAAC;EAChC,CAAC,CAAC,OAAOK,GAAG,EAAE;IACZ,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCrB,IAAI,CAAC,KAAK,EAAG,mBAAmB,GAAGa,GAAG,GAAG,wBAAyB,CAAC;IACrE;EACF;EACA,OAAOA,GAAG;AACZ;AAEA,SAASS,YAAYA,CACnBC,KAAK,EACLC,UAAU,EACVC,WAAW,EACX;EACA,IAAKD,UAAU,KAAK,KAAK,CAAC,EAAGA,UAAU,GAAG,CAAC,CAAC;EAE5C,IAAIE,KAAK,GAAGD,WAAW,IAAIE,UAAU;EACrC,IAAIC,WAAW;EACf,IAAI;IACFA,WAAW,GAAGF,KAAK,CAACH,KAAK,IAAI,EAAE,CAAC;EAClC,CAAC,CAAC,OAAOM,CAAC,EAAE;IACVV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIrB,IAAI,CAAC,KAAK,EAAE6B,CAAC,CAAC/B,OAAO,CAAC;IAC/D8B,WAAW,GAAG,CAAC,CAAC;EAClB;EACA,KAAK,IAAIvB,GAAG,IAAImB,UAAU,EAAE;IAC1B,IAAIM,KAAK,GAAGN,UAAU,CAACnB,GAAG,CAAC;IAC3BuB,WAAW,CAACvB,GAAG,CAAC,GAAG0B,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GACnCA,KAAK,CAACG,GAAG,CAACC,mBAAmB,CAAC,GAC9BA,mBAAmB,CAACJ,KAAK,CAAC;EAChC;EACA,OAAOF,WAAW;AACpB;AAEA,IAAIM,mBAAmB,GAAG,SAAAA,CAAUJ,KAAK,EAAE;EAAE,OAAQA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGK,MAAM,CAACL,KAAK,CAAC;AAAG,CAAC;AAE3H,SAASH,UAAUA,CAAEJ,KAAK,EAAE;EAC1B,IAAIa,GAAG,GAAG,CAAC,CAAC;EAEZb,KAAK,GAAGA,KAAK,CAACc,IAAI,CAAC,CAAC,CAACtB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAE7C,IAAI,CAACQ,KAAK,EAAE;IACV,OAAOa,GAAG;EACZ;EAEAb,KAAK,CAACe,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;IACxC,IAAIC,KAAK,GAAGD,KAAK,CAACzB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACuB,KAAK,CAAC,GAAG,CAAC;IAChD,IAAIjC,GAAG,GAAGW,MAAM,CAACyB,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;IAC/B,IAAIC,GAAG,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,GAAG5B,MAAM,CAACyB,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IAE3D,IAAIT,GAAG,CAAC/B,GAAG,CAAC,KAAKyC,SAAS,EAAE;MAC1BV,GAAG,CAAC/B,GAAG,CAAC,GAAGsC,GAAG;IAChB,CAAC,MAAM,IAAIZ,KAAK,CAACC,OAAO,CAACI,GAAG,CAAC/B,GAAG,CAAC,CAAC,EAAE;MAClC+B,GAAG,CAAC/B,GAAG,CAAC,CAAC0C,IAAI,CAACJ,GAAG,CAAC;IACpB,CAAC,MAAM;MACLP,GAAG,CAAC/B,GAAG,CAAC,GAAG,CAAC+B,GAAG,CAAC/B,GAAG,CAAC,EAAEsC,GAAG,CAAC;IAC5B;EACF,CAAC,CAAC;EAEF,OAAOP,GAAG;AACZ;AAEA,SAASY,cAAcA,CAAEC,GAAG,EAAE;EAC5B,IAAIb,GAAG,GAAGa,GAAG,GACTC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACfhB,GAAG,CAAC,UAAU5B,GAAG,EAAE;IAClB,IAAIsC,GAAG,GAAGM,GAAG,CAAC5C,GAAG,CAAC;IAElB,IAAIsC,GAAG,KAAKG,SAAS,EAAE;MACrB,OAAO,EAAE;IACX;IAEA,IAAIH,GAAG,KAAK,IAAI,EAAE;MAChB,OAAO/B,MAAM,CAACP,GAAG,CAAC;IACpB;IAEA,IAAI0B,KAAK,CAACC,OAAO,CAACW,GAAG,CAAC,EAAE;MACtB,IAAIS,MAAM,GAAG,EAAE;MACfT,GAAG,CAACJ,OAAO,CAAC,UAAUc,IAAI,EAAE;QAC1B,IAAIA,IAAI,KAAKP,SAAS,EAAE;UACtB;QACF;QACA,IAAIO,IAAI,KAAK,IAAI,EAAE;UACjBD,MAAM,CAACL,IAAI,CAACnC,MAAM,CAACP,GAAG,CAAC,CAAC;QAC1B,CAAC,MAAM;UACL+C,MAAM,CAACL,IAAI,CAACnC,MAAM,CAACP,GAAG,CAAC,GAAG,GAAG,GAAGO,MAAM,CAACyC,IAAI,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC;MACF,OAAOD,MAAM,CAACP,IAAI,CAAC,GAAG,CAAC;IACzB;IAEA,OAAOjC,MAAM,CAACP,GAAG,CAAC,GAAG,GAAG,GAAGO,MAAM,CAAC+B,GAAG,CAAC;EACxC,CAAC,CAAC,CACDW,MAAM,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACX,MAAM,GAAG,CAAC;EAAE,CAAC,CAAC,CAC7CC,IAAI,CAAC,GAAG,CAAC,GACV,IAAI;EACR,OAAOT,GAAG,GAAI,GAAG,GAAGA,GAAG,GAAI,EAAE;AAC/B;;AAEA;;AAEA,IAAIoB,eAAe,GAAG,MAAM;AAE5B,SAASC,WAAWA,CAClBC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,MAAM,EACN;EACA,IAAIb,cAAc,GAAGa,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACd,cAAc;EAE5D,IAAIzB,KAAK,GAAGoC,QAAQ,CAACpC,KAAK,IAAI,CAAC,CAAC;EAChC,IAAI;IACFA,KAAK,GAAGwC,KAAK,CAACxC,KAAK,CAAC;EACtB,CAAC,CAAC,OAAOM,CAAC,EAAE,CAAC;EAEb,IAAImC,KAAK,GAAG;IACVC,IAAI,EAAEN,QAAQ,CAACM,IAAI,IAAKP,MAAM,IAAIA,MAAM,CAACO,IAAK;IAC9CC,IAAI,EAAGR,MAAM,IAAIA,MAAM,CAACQ,IAAI,IAAK,CAAC,CAAC;IACnCC,IAAI,EAAER,QAAQ,CAACQ,IAAI,IAAI,GAAG;IAC1BC,IAAI,EAAET,QAAQ,CAACS,IAAI,IAAI,EAAE;IACzB7C,KAAK,EAAEA,KAAK;IACZ8C,MAAM,EAAEV,QAAQ,CAACU,MAAM,IAAI,CAAC,CAAC;IAC7BC,QAAQ,EAAEC,WAAW,CAACZ,QAAQ,EAAEX,cAAc,CAAC;IAC/CwB,OAAO,EAAEd,MAAM,GAAGe,WAAW,CAACf,MAAM,CAAC,GAAG;EAC1C,CAAC;EACD,IAAIE,cAAc,EAAE;IAClBI,KAAK,CAACJ,cAAc,GAAGW,WAAW,CAACX,cAAc,EAAEZ,cAAc,CAAC;EACpE;EACA,OAAOE,MAAM,CAACwB,MAAM,CAACV,KAAK,CAAC;AAC7B;AAEA,SAASD,KAAKA,CAAEjC,KAAK,EAAE;EACrB,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAACG,GAAG,CAAC8B,KAAK,CAAC;EACzB,CAAC,MAAM,IAAIjC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7C,IAAIM,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,IAAI/B,GAAG,IAAIyB,KAAK,EAAE;MACrBM,GAAG,CAAC/B,GAAG,CAAC,GAAG0D,KAAK,CAACjC,KAAK,CAACzB,GAAG,CAAC,CAAC;IAC9B;IACA,OAAO+B,GAAG;EACZ,CAAC,MAAM;IACL,OAAON,KAAK;EACd;AACF;;AAEA;AACA,IAAI6C,KAAK,GAAGlB,WAAW,CAAC,IAAI,EAAE;EAC5BU,IAAI,EAAE;AACR,CAAC,CAAC;AAEF,SAASM,WAAWA,CAAEf,MAAM,EAAE;EAC5B,IAAItB,GAAG,GAAG,EAAE;EACZ,OAAOsB,MAAM,EAAE;IACbtB,GAAG,CAACwC,OAAO,CAAClB,MAAM,CAAC;IACnBA,MAAM,GAAGA,MAAM,CAACmB,MAAM;EACxB;EACA,OAAOzC,GAAG;AACZ;AAEA,SAASmC,WAAWA,CAClBO,GAAG,EACHC,eAAe,EACf;EACA,IAAIZ,IAAI,GAAGW,GAAG,CAACX,IAAI;EACnB,IAAI5C,KAAK,GAAGuD,GAAG,CAACvD,KAAK;EAAE,IAAKA,KAAK,KAAK,KAAK,CAAC,EAAGA,KAAK,GAAG,CAAC,CAAC;EACzD,IAAI6C,IAAI,GAAGU,GAAG,CAACV,IAAI;EAAE,IAAKA,IAAI,KAAK,KAAK,CAAC,EAAGA,IAAI,GAAG,EAAE;EAErD,IAAIY,SAAS,GAAGD,eAAe,IAAI/B,cAAc;EACjD,OAAO,CAACmB,IAAI,IAAI,GAAG,IAAIa,SAAS,CAACzD,KAAK,CAAC,GAAG6C,IAAI;AAChD;AAEA,SAASa,WAAWA,CAAE9E,CAAC,EAAEC,CAAC,EAAE8E,QAAQ,EAAE;EACpC,IAAI9E,CAAC,KAAKuE,KAAK,EAAE;IACf,OAAOxE,CAAC,KAAKC,CAAC;EAChB,CAAC,MAAM,IAAI,CAACA,CAAC,EAAE;IACb,OAAO,KAAK;EACd,CAAC,MAAM,IAAID,CAAC,CAACgE,IAAI,IAAI/D,CAAC,CAAC+D,IAAI,EAAE;IAC3B,OAAOhE,CAAC,CAACgE,IAAI,CAACpD,OAAO,CAACyC,eAAe,EAAE,EAAE,CAAC,KAAKpD,CAAC,CAAC+D,IAAI,CAACpD,OAAO,CAACyC,eAAe,EAAE,EAAE,CAAC,KAAK0B,QAAQ,IAC7F/E,CAAC,CAACiE,IAAI,KAAKhE,CAAC,CAACgE,IAAI,IACjBe,aAAa,CAAChF,CAAC,CAACoB,KAAK,EAAEnB,CAAC,CAACmB,KAAK,CAAC,CAAC;EACpC,CAAC,MAAM,IAAIpB,CAAC,CAAC8D,IAAI,IAAI7D,CAAC,CAAC6D,IAAI,EAAE;IAC3B,OACE9D,CAAC,CAAC8D,IAAI,KAAK7D,CAAC,CAAC6D,IAAI,KAChBiB,QAAQ,IACP/E,CAAC,CAACiE,IAAI,KAAKhE,CAAC,CAACgE,IAAI,IACnBe,aAAa,CAAChF,CAAC,CAACoB,KAAK,EAAEnB,CAAC,CAACmB,KAAK,CAAC,IAC/B4D,aAAa,CAAChF,CAAC,CAACkE,MAAM,EAAEjE,CAAC,CAACiE,MAAM,CAAE,CACjC;EAEL,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAEA,SAASc,aAAaA,CAAEhF,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAKD,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC,CAAC;EAC1B,IAAKC,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC,CAAC;;EAE1B;EACA,IAAI,CAACD,CAAC,IAAI,CAACC,CAAC,EAAE;IAAE,OAAOD,CAAC,KAAKC,CAAC;EAAC;EAC/B,IAAIgF,KAAK,GAAGlC,MAAM,CAACC,IAAI,CAAChD,CAAC,CAAC,CAACkF,IAAI,CAAC,CAAC;EACjC,IAAIC,KAAK,GAAGpC,MAAM,CAACC,IAAI,CAAC/C,CAAC,CAAC,CAACiF,IAAI,CAAC,CAAC;EACjC,IAAID,KAAK,CAACxC,MAAM,KAAK0C,KAAK,CAAC1C,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EACA,OAAOwC,KAAK,CAACG,KAAK,CAAC,UAAUlF,GAAG,EAAEmF,CAAC,EAAE;IACnC,IAAIC,IAAI,GAAGtF,CAAC,CAACE,GAAG,CAAC;IACjB,IAAIqF,IAAI,GAAGJ,KAAK,CAACE,CAAC,CAAC;IACnB,IAAIE,IAAI,KAAKrF,GAAG,EAAE;MAAE,OAAO,KAAK;IAAC;IACjC,IAAIsF,IAAI,GAAGvF,CAAC,CAACC,GAAG,CAAC;IACjB;IACA,IAAIoF,IAAI,IAAI,IAAI,IAAIE,IAAI,IAAI,IAAI,EAAE;MAAE,OAAOF,IAAI,KAAKE,IAAI;IAAC;IACzD;IACA,IAAI,OAAOF,IAAI,KAAK,QAAQ,IAAI,OAAOE,IAAI,KAAK,QAAQ,EAAE;MACxD,OAAOR,aAAa,CAACM,IAAI,EAAEE,IAAI,CAAC;IAClC;IACA,OAAOxD,MAAM,CAACsD,IAAI,CAAC,KAAKtD,MAAM,CAACwD,IAAI,CAAC;EACtC,CAAC,CAAC;AACJ;AAEA,SAASC,eAAeA,CAAEC,OAAO,EAAEC,MAAM,EAAE;EACzC,OACED,OAAO,CAAC1B,IAAI,CAACpD,OAAO,CAACyC,eAAe,EAAE,GAAG,CAAC,CAACuC,OAAO,CAChDD,MAAM,CAAC3B,IAAI,CAACpD,OAAO,CAACyC,eAAe,EAAE,GAAG,CAC1C,CAAC,KAAK,CAAC,KACN,CAACsC,MAAM,CAAC1B,IAAI,IAAIyB,OAAO,CAACzB,IAAI,KAAK0B,MAAM,CAAC1B,IAAI,CAAC,IAC9C4B,aAAa,CAACH,OAAO,CAACtE,KAAK,EAAEuE,MAAM,CAACvE,KAAK,CAAC;AAE9C;AAEA,SAASyE,aAAaA,CAAEH,OAAO,EAAEC,MAAM,EAAE;EACvC,KAAK,IAAIzF,GAAG,IAAIyF,MAAM,EAAE;IACtB,IAAI,EAAEzF,GAAG,IAAIwF,OAAO,CAAC,EAAE;MACrB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASI,kBAAkBA,CAAEjC,KAAK,EAAE;EAClC,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,KAAK,CAACQ,OAAO,CAAC5B,MAAM,EAAE4C,CAAC,EAAE,EAAE;IAC7C,IAAI9B,MAAM,GAAGM,KAAK,CAACQ,OAAO,CAACgB,CAAC,CAAC;IAC7B,KAAK,IAAIvB,IAAI,IAAIP,MAAM,CAACwC,SAAS,EAAE;MACjC,IAAIC,QAAQ,GAAGzC,MAAM,CAACwC,SAAS,CAACjC,IAAI,CAAC;MACrC,IAAImC,GAAG,GAAG1C,MAAM,CAAC2C,UAAU,CAACpC,IAAI,CAAC;MACjC,IAAI,CAACkC,QAAQ,IAAI,CAACC,GAAG,EAAE;QAAE;MAAS;MAClC,OAAO1C,MAAM,CAAC2C,UAAU,CAACpC,IAAI,CAAC;MAC9B,KAAK,IAAIqC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,GAAG,CAACxD,MAAM,EAAE0D,GAAG,EAAE,EAAE;QACzC,IAAI,CAACH,QAAQ,CAACI,iBAAiB,EAAE;UAAEH,GAAG,CAACE,GAAG,CAAC,CAACH,QAAQ,CAAC;QAAE;MACzD;IACF;EACF;AACF;AAEA,IAAIK,IAAI,GAAG;EACTvC,IAAI,EAAE,YAAY;EAClBwC,UAAU,EAAE,IAAI;EAChBC,KAAK,EAAE;IACLzC,IAAI,EAAE;MACJ0C,IAAI,EAAExE,MAAM;MACZyE,OAAO,EAAE;IACX;EACF,CAAC;EACDC,MAAM,EAAE,SAASA,MAAMA,CAAEC,CAAC,EAAEhC,GAAG,EAAE;IAC/B,IAAI4B,KAAK,GAAG5B,GAAG,CAAC4B,KAAK;IACrB,IAAIK,QAAQ,GAAGjC,GAAG,CAACiC,QAAQ;IAC3B,IAAIlC,MAAM,GAAGC,GAAG,CAACD,MAAM;IACvB,IAAImC,IAAI,GAAGlC,GAAG,CAACkC,IAAI;;IAEnB;IACAA,IAAI,CAACC,UAAU,GAAG,IAAI;;IAEtB;IACA;IACA,IAAIC,CAAC,GAAGrC,MAAM,CAACsC,cAAc;IAC7B,IAAIlD,IAAI,GAAGyC,KAAK,CAACzC,IAAI;IACrB,IAAID,KAAK,GAAGa,MAAM,CAACuC,MAAM;IACzB,IAAIC,KAAK,GAAGxC,MAAM,CAACyC,gBAAgB,KAAKzC,MAAM,CAACyC,gBAAgB,GAAG,CAAC,CAAC,CAAC;;IAErE;IACA;IACA,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ,GAAG,KAAK;IACpB,OAAO3C,MAAM,IAAIA,MAAM,CAAC4C,WAAW,KAAK5C,MAAM,EAAE;MAC9C,IAAI6C,SAAS,GAAG7C,MAAM,CAAC8C,MAAM,GAAG9C,MAAM,CAAC8C,MAAM,CAACX,IAAI,GAAG,CAAC,CAAC;MACvD,IAAIU,SAAS,CAACT,UAAU,EAAE;QACxBM,KAAK,EAAE;MACT;MACA,IAAIG,SAAS,CAACE,SAAS,IAAI/C,MAAM,CAACgD,eAAe,IAAIhD,MAAM,CAACiD,SAAS,EAAE;QACrEN,QAAQ,GAAG,IAAI;MACjB;MACA3C,MAAM,GAAGA,MAAM,CAACkD,OAAO;IACzB;IACAf,IAAI,CAACgB,eAAe,GAAGT,KAAK;;IAE5B;IACA,IAAIC,QAAQ,EAAE;MACZ,IAAIS,UAAU,GAAGZ,KAAK,CAACpD,IAAI,CAAC;MAC5B,IAAIiE,eAAe,GAAGD,UAAU,IAAIA,UAAU,CAACE,SAAS;MACxD,IAAID,eAAe,EAAE;QACnB;QACA;QACA,IAAID,UAAU,CAACG,WAAW,EAAE;UAC1BC,eAAe,CAACH,eAAe,EAAElB,IAAI,EAAEiB,UAAU,CAACjE,KAAK,EAAEiE,UAAU,CAACG,WAAW,CAAC;QAClF;QACA,OAAOlB,CAAC,CAACgB,eAAe,EAAElB,IAAI,EAAED,QAAQ,CAAC;MAC3C,CAAC,MAAM;QACL;QACA,OAAOG,CAAC,CAAC,CAAC;MACZ;IACF;IAEA,IAAI1C,OAAO,GAAGR,KAAK,CAACQ,OAAO,CAAC+C,KAAK,CAAC;IAClC,IAAIY,SAAS,GAAG3D,OAAO,IAAIA,OAAO,CAAC8D,UAAU,CAACrE,IAAI,CAAC;;IAEnD;IACA,IAAI,CAACO,OAAO,IAAI,CAAC2D,SAAS,EAAE;MAC1Bd,KAAK,CAACpD,IAAI,CAAC,GAAG,IAAI;MAClB,OAAOiD,CAAC,CAAC,CAAC;IACZ;;IAEA;IACAG,KAAK,CAACpD,IAAI,CAAC,GAAG;MAAEkE,SAAS,EAAEA;IAAU,CAAC;;IAEtC;IACA;IACAnB,IAAI,CAACuB,qBAAqB,GAAG,UAAUC,EAAE,EAAE7F,GAAG,EAAE;MAC9C;MACA,IAAIkD,OAAO,GAAGrB,OAAO,CAAC0B,SAAS,CAACjC,IAAI,CAAC;MACrC,IACGtB,GAAG,IAAIkD,OAAO,KAAK2C,EAAE,IACrB,CAAC7F,GAAG,IAAIkD,OAAO,KAAK2C,EAAG,EACxB;QACAhE,OAAO,CAAC0B,SAAS,CAACjC,IAAI,CAAC,GAAGtB,GAAG;MAC/B;IACF;;IAEA;IACA;IAAA;IACC,CAACqE,IAAI,CAACyB,IAAI,KAAKzB,IAAI,CAACyB,IAAI,GAAG,CAAC,CAAC,CAAC,EAAEC,QAAQ,GAAG,UAAU5B,CAAC,EAAE6B,KAAK,EAAE;MAC9DnE,OAAO,CAAC0B,SAAS,CAACjC,IAAI,CAAC,GAAG0E,KAAK,CAACC,iBAAiB;IACnD,CAAC;;IAED;IACA;IACA5B,IAAI,CAACyB,IAAI,CAACI,IAAI,GAAG,UAAUF,KAAK,EAAE;MAChC,IAAIA,KAAK,CAAC3B,IAAI,CAACY,SAAS,IACtBe,KAAK,CAACC,iBAAiB,IACvBD,KAAK,CAACC,iBAAiB,KAAKpE,OAAO,CAAC0B,SAAS,CAACjC,IAAI,CAAC,EACnD;QACAO,OAAO,CAAC0B,SAAS,CAACjC,IAAI,CAAC,GAAG0E,KAAK,CAACC,iBAAiB;MACnD;;MAEA;MACA;MACA;MACA3C,kBAAkB,CAACjC,KAAK,CAAC;IAC3B,CAAC;IAED,IAAIoE,WAAW,GAAG5D,OAAO,CAACkC,KAAK,IAAIlC,OAAO,CAACkC,KAAK,CAACzC,IAAI,CAAC;IACtD;IACA,IAAImE,WAAW,EAAE;MACflI,MAAM,CAACmH,KAAK,CAACpD,IAAI,CAAC,EAAE;QAClBD,KAAK,EAAEA,KAAK;QACZoE,WAAW,EAAEA;MACf,CAAC,CAAC;MACFC,eAAe,CAACF,SAAS,EAAEnB,IAAI,EAAEhD,KAAK,EAAEoE,WAAW,CAAC;IACtD;IAEA,OAAOlB,CAAC,CAACiB,SAAS,EAAEnB,IAAI,EAAED,QAAQ,CAAC;EACrC;AACF,CAAC;AAED,SAASsB,eAAeA,CAAEF,SAAS,EAAEnB,IAAI,EAAEhD,KAAK,EAAEoE,WAAW,EAAE;EAC7D;EACA,IAAIU,WAAW,GAAG9B,IAAI,CAACN,KAAK,GAAGqC,YAAY,CAAC/E,KAAK,EAAEoE,WAAW,CAAC;EAC/D,IAAIU,WAAW,EAAE;IACf;IACAA,WAAW,GAAG9B,IAAI,CAACN,KAAK,GAAGxG,MAAM,CAAC,CAAC,CAAC,EAAE4I,WAAW,CAAC;IAClD;IACA,IAAIE,KAAK,GAAGhC,IAAI,CAACgC,KAAK,GAAGhC,IAAI,CAACgC,KAAK,IAAI,CAAC,CAAC;IACzC,KAAK,IAAI3I,GAAG,IAAIyI,WAAW,EAAE;MAC3B,IAAI,CAACX,SAAS,CAACzB,KAAK,IAAI,EAAErG,GAAG,IAAI8H,SAAS,CAACzB,KAAK,CAAC,EAAE;QACjDsC,KAAK,CAAC3I,GAAG,CAAC,GAAGyI,WAAW,CAACzI,GAAG,CAAC;QAC7B,OAAOyI,WAAW,CAACzI,GAAG,CAAC;MACzB;IACF;EACF;AACF;AAEA,SAAS0I,YAAYA,CAAE/E,KAAK,EAAEiF,MAAM,EAAE;EACpC,QAAQ,OAAOA,MAAM;IACnB,KAAK,WAAW;MACd;IACF,KAAK,QAAQ;MACX,OAAOA,MAAM;IACf,KAAK,UAAU;MACb,OAAOA,MAAM,CAACjF,KAAK,CAAC;IACtB,KAAK,SAAS;MACZ,OAAOiF,MAAM,GAAGjF,KAAK,CAACK,MAAM,GAAGvB,SAAS;IAC1C;MACE,IAAI3B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCrB,IAAI,CACF,KAAK,EACL,aAAa,GAAIgE,KAAK,CAACG,IAAK,GAAG,UAAU,GAAI,OAAO8E,MAAO,GAAG,IAAI,GAClE,2CACF,CAAC;MACH;EACJ;AACF;;AAEA;;AAEA,SAASC,WAAWA,CAClBC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACN;EACA,IAAIC,SAAS,GAAGH,QAAQ,CAACI,MAAM,CAAC,CAAC,CAAC;EAClC,IAAID,SAAS,KAAK,GAAG,EAAE;IACrB,OAAOH,QAAQ;EACjB;EAEA,IAAIG,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,GAAG,EAAE;IAC1C,OAAOF,IAAI,GAAGD,QAAQ;EACxB;EAEA,IAAIK,KAAK,GAAGJ,IAAI,CAAC9G,KAAK,CAAC,GAAG,CAAC;;EAE3B;EACA;EACA;EACA,IAAI,CAAC+G,MAAM,IAAI,CAACG,KAAK,CAACA,KAAK,CAAC5G,MAAM,GAAG,CAAC,CAAC,EAAE;IACvC4G,KAAK,CAACC,GAAG,CAAC,CAAC;EACb;;EAEA;EACA,IAAIC,QAAQ,GAAGP,QAAQ,CAACpI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACuB,KAAK,CAAC,GAAG,CAAC;EACrD,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkE,QAAQ,CAAC9G,MAAM,EAAE4C,CAAC,EAAE,EAAE;IACxC,IAAImE,OAAO,GAAGD,QAAQ,CAAClE,CAAC,CAAC;IACzB,IAAImE,OAAO,KAAK,IAAI,EAAE;MACpBH,KAAK,CAACC,GAAG,CAAC,CAAC;IACb,CAAC,MAAM,IAAIE,OAAO,KAAK,GAAG,EAAE;MAC1BH,KAAK,CAACzG,IAAI,CAAC4G,OAAO,CAAC;IACrB;EACF;;EAEA;EACA,IAAIH,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;IACnBA,KAAK,CAAC5E,OAAO,CAAC,EAAE,CAAC;EACnB;EAEA,OAAO4E,KAAK,CAAC3G,IAAI,CAAC,GAAG,CAAC;AACxB;AAEA,SAAS+G,SAASA,CAAEzF,IAAI,EAAE;EACxB,IAAIC,IAAI,GAAG,EAAE;EACb,IAAI7C,KAAK,GAAG,EAAE;EAEd,IAAIsI,SAAS,GAAG1F,IAAI,CAAC4B,OAAO,CAAC,GAAG,CAAC;EACjC,IAAI8D,SAAS,IAAI,CAAC,EAAE;IAClBzF,IAAI,GAAGD,IAAI,CAAC2F,KAAK,CAACD,SAAS,CAAC;IAC5B1F,IAAI,GAAGA,IAAI,CAAC2F,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC;EACjC;EAEA,IAAIE,UAAU,GAAG5F,IAAI,CAAC4B,OAAO,CAAC,GAAG,CAAC;EAClC,IAAIgE,UAAU,IAAI,CAAC,EAAE;IACnBxI,KAAK,GAAG4C,IAAI,CAAC2F,KAAK,CAACC,UAAU,GAAG,CAAC,CAAC;IAClC5F,IAAI,GAAGA,IAAI,CAAC2F,KAAK,CAAC,CAAC,EAAEC,UAAU,CAAC;EAClC;EAEA,OAAO;IACL5F,IAAI,EAAEA,IAAI;IACV5C,KAAK,EAAEA,KAAK;IACZ6C,IAAI,EAAEA;EACR,CAAC;AACH;AAEA,SAAS4F,SAASA,CAAE7F,IAAI,EAAE;EACxB,OAAOA,IAAI,CAACpD,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC;AAC3C;AAEA,IAAIkJ,OAAO,GAAGlI,KAAK,CAACC,OAAO,IAAI,UAAUkI,GAAG,EAAE;EAC5C,OAAOhH,MAAM,CAACiH,SAAS,CAACzJ,QAAQ,CAAC0J,IAAI,CAACF,GAAG,CAAC,IAAI,gBAAgB;AAChE,CAAC;;AAED;AACA;AACA;AACA,IAAIG,cAAc,GAAGC,YAAY;AACjC,IAAIC,OAAO,GAAG7I,KAAK;AACnB,IAAI8I,SAAS,GAAGC,OAAO;AACvB,IAAIC,kBAAkB,GAAGC,gBAAgB;AACzC,IAAIC,gBAAgB,GAAGC,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,IAAIC,MAAM,CAAC;AAC3B;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,wGAAwG,CACzG,CAAClI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnB,KAAKA,CAAEb,GAAG,EAAEiD,OAAO,EAAE;EAC5B,IAAIkH,MAAM,GAAG,EAAE;EACf,IAAI3K,GAAG,GAAG,CAAC;EACX,IAAI4K,KAAK,GAAG,CAAC;EACb,IAAI9G,IAAI,GAAG,EAAE;EACb,IAAI+G,gBAAgB,GAAGpH,OAAO,IAAIA,OAAO,CAACqH,SAAS,IAAI,GAAG;EAC1D,IAAI/I,GAAG;EAEP,OAAO,CAACA,GAAG,GAAG0I,WAAW,CAACM,IAAI,CAACvK,GAAG,CAAC,KAAK,IAAI,EAAE;IAC5C,IAAIwK,CAAC,GAAGjJ,GAAG,CAAC,CAAC,CAAC;IACd,IAAIkJ,OAAO,GAAGlJ,GAAG,CAAC,CAAC,CAAC;IACpB,IAAImJ,MAAM,GAAGnJ,GAAG,CAAC6I,KAAK;IACtB9G,IAAI,IAAItD,GAAG,CAACiJ,KAAK,CAACmB,KAAK,EAAEM,MAAM,CAAC;IAChCN,KAAK,GAAGM,MAAM,GAAGF,CAAC,CAACzI,MAAM;;IAEzB;IACA,IAAI0I,OAAO,EAAE;MACXnH,IAAI,IAAImH,OAAO,CAAC,CAAC,CAAC;MAClB;IACF;IAEA,IAAIE,IAAI,GAAG3K,GAAG,CAACoK,KAAK,CAAC;IACrB,IAAIQ,MAAM,GAAGrJ,GAAG,CAAC,CAAC,CAAC;IACnB,IAAI6B,IAAI,GAAG7B,GAAG,CAAC,CAAC,CAAC;IACjB,IAAIsJ,OAAO,GAAGtJ,GAAG,CAAC,CAAC,CAAC;IACpB,IAAIuJ,KAAK,GAAGvJ,GAAG,CAAC,CAAC,CAAC;IAClB,IAAIwJ,QAAQ,GAAGxJ,GAAG,CAAC,CAAC,CAAC;IACrB,IAAIyJ,QAAQ,GAAGzJ,GAAG,CAAC,CAAC,CAAC;;IAErB;IACA,IAAI+B,IAAI,EAAE;MACR6G,MAAM,CAACjI,IAAI,CAACoB,IAAI,CAAC;MACjBA,IAAI,GAAG,EAAE;IACX;IAEA,IAAI2H,OAAO,GAAGL,MAAM,IAAI,IAAI,IAAID,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAKC,MAAM;IAC/D,IAAIM,MAAM,GAAGH,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,GAAG;IACjD,IAAII,QAAQ,GAAGJ,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,GAAG;IACnD,IAAIT,SAAS,GAAG/I,GAAG,CAAC,CAAC,CAAC,IAAI8I,gBAAgB;IAC1C,IAAIe,OAAO,GAAGP,OAAO,IAAIC,KAAK;IAE9BX,MAAM,CAACjI,IAAI,CAAC;MACVkB,IAAI,EAAEA,IAAI,IAAI5D,GAAG,EAAE;MACnBoL,MAAM,EAAEA,MAAM,IAAI,EAAE;MACpBN,SAAS,EAAEA,SAAS;MACpBa,QAAQ,EAAEA,QAAQ;MAClBD,MAAM,EAAEA,MAAM;MACdD,OAAO,EAAEA,OAAO;MAChBD,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBI,OAAO,EAAEA,OAAO,GAAGC,WAAW,CAACD,OAAO,CAAC,GAAIJ,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAGM,YAAY,CAAChB,SAAS,CAAC,GAAG;IAChG,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIF,KAAK,GAAGpK,GAAG,CAAC+B,MAAM,EAAE;IACtBuB,IAAI,IAAItD,GAAG,CAACuL,MAAM,CAACnB,KAAK,CAAC;EAC3B;;EAEA;EACA,IAAI9G,IAAI,EAAE;IACR6G,MAAM,CAACjI,IAAI,CAACoB,IAAI,CAAC;EACnB;EAEA,OAAO6G,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,OAAOA,CAAE5J,GAAG,EAAEiD,OAAO,EAAE;EAC9B,OAAO6G,gBAAgB,CAACjJ,KAAK,CAACb,GAAG,EAAEiD,OAAO,CAAC,EAAEA,OAAO,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuI,wBAAwBA,CAAExL,GAAG,EAAE;EACtC,OAAOyL,SAAS,CAACzL,GAAG,CAAC,CAACE,OAAO,CAAC,SAAS,EAAE,UAAUP,CAAC,EAAE;IACpD,OAAO,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC6L,WAAW,CAAC,CAAC;EACzD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAE3L,GAAG,EAAE;EAC5B,OAAOyL,SAAS,CAACzL,GAAG,CAAC,CAACE,OAAO,CAAC,OAAO,EAAE,UAAUP,CAAC,EAAE;IAClD,OAAO,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAAC6L,WAAW,CAAC,CAAC;EACzD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,SAAS5B,gBAAgBA,CAAEK,MAAM,EAAElH,OAAO,EAAE;EAC1C;EACA,IAAI2I,OAAO,GAAG,IAAI1K,KAAK,CAACiJ,MAAM,CAACpI,MAAM,CAAC;;EAEtC;EACA,KAAK,IAAI4C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwF,MAAM,CAACpI,MAAM,EAAE4C,CAAC,EAAE,EAAE;IACtC,IAAI,OAAOwF,MAAM,CAACxF,CAAC,CAAC,KAAK,QAAQ,EAAE;MACjCiH,OAAO,CAACjH,CAAC,CAAC,GAAG,IAAIuF,MAAM,CAAC,MAAM,GAAGC,MAAM,CAACxF,CAAC,CAAC,CAACyG,OAAO,GAAG,IAAI,EAAES,KAAK,CAAC5I,OAAO,CAAC,CAAC;IAC5E;EACF;EAEA,OAAO,UAAUb,GAAG,EAAE0J,IAAI,EAAE;IAC1B,IAAIxI,IAAI,GAAG,EAAE;IACb,IAAI6C,IAAI,GAAG/D,GAAG,IAAI,CAAC,CAAC;IACpB,IAAIa,OAAO,GAAG6I,IAAI,IAAI,CAAC,CAAC;IACxB,IAAI/L,MAAM,GAAGkD,OAAO,CAAC8I,MAAM,GAAGP,wBAAwB,GAAGvL,kBAAkB;IAE3E,KAAK,IAAI0E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwF,MAAM,CAACpI,MAAM,EAAE4C,CAAC,EAAE,EAAE;MACtC,IAAIqH,KAAK,GAAG7B,MAAM,CAACxF,CAAC,CAAC;MAErB,IAAI,OAAOqH,KAAK,KAAK,QAAQ,EAAE;QAC7B1I,IAAI,IAAI0I,KAAK;QAEb;MACF;MAEA,IAAI/K,KAAK,GAAGkF,IAAI,CAAC6F,KAAK,CAAC5I,IAAI,CAAC;MAC5B,IAAI0F,OAAO;MAEX,IAAI7H,KAAK,IAAI,IAAI,EAAE;QACjB,IAAI+K,KAAK,CAACb,QAAQ,EAAE;UAClB;UACA,IAAIa,KAAK,CAACf,OAAO,EAAE;YACjB3H,IAAI,IAAI0I,KAAK,CAACpB,MAAM;UACtB;UAEA;QACF,CAAC,MAAM;UACL,MAAM,IAAIqB,SAAS,CAAC,YAAY,GAAGD,KAAK,CAAC5I,IAAI,GAAG,iBAAiB,CAAC;QACpE;MACF;MAEA,IAAIgG,OAAO,CAACnI,KAAK,CAAC,EAAE;QAClB,IAAI,CAAC+K,KAAK,CAACd,MAAM,EAAE;UACjB,MAAM,IAAIe,SAAS,CAAC,YAAY,GAAGD,KAAK,CAAC5I,IAAI,GAAG,iCAAiC,GAAG8I,IAAI,CAAC/H,SAAS,CAAClD,KAAK,CAAC,GAAG,GAAG,CAAC;QAClH;QAEA,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;UACtB,IAAIiK,KAAK,CAACb,QAAQ,EAAE;YAClB;UACF,CAAC,MAAM;YACL,MAAM,IAAIc,SAAS,CAAC,YAAY,GAAGD,KAAK,CAAC5I,IAAI,GAAG,mBAAmB,CAAC;UACtE;QACF;QAEA,KAAK,IAAI+I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlL,KAAK,CAACc,MAAM,EAAEoK,CAAC,EAAE,EAAE;UACrCrD,OAAO,GAAG/I,MAAM,CAACkB,KAAK,CAACkL,CAAC,CAAC,CAAC;UAE1B,IAAI,CAACP,OAAO,CAACjH,CAAC,CAAC,CAACyH,IAAI,CAACtD,OAAO,CAAC,EAAE;YAC7B,MAAM,IAAImD,SAAS,CAAC,gBAAgB,GAAGD,KAAK,CAAC5I,IAAI,GAAG,cAAc,GAAG4I,KAAK,CAACZ,OAAO,GAAG,mBAAmB,GAAGc,IAAI,CAAC/H,SAAS,CAAC2E,OAAO,CAAC,GAAG,GAAG,CAAC;UAC3I;UAEAxF,IAAI,IAAI,CAAC6I,CAAC,KAAK,CAAC,GAAGH,KAAK,CAACpB,MAAM,GAAGoB,KAAK,CAAC1B,SAAS,IAAIxB,OAAO;QAC9D;QAEA;MACF;MAEAA,OAAO,GAAGkD,KAAK,CAAChB,QAAQ,GAAGW,cAAc,CAAC1K,KAAK,CAAC,GAAGlB,MAAM,CAACkB,KAAK,CAAC;MAEhE,IAAI,CAAC2K,OAAO,CAACjH,CAAC,CAAC,CAACyH,IAAI,CAACtD,OAAO,CAAC,EAAE;QAC7B,MAAM,IAAImD,SAAS,CAAC,YAAY,GAAGD,KAAK,CAAC5I,IAAI,GAAG,cAAc,GAAG4I,KAAK,CAACZ,OAAO,GAAG,mBAAmB,GAAGtC,OAAO,GAAG,GAAG,CAAC;MACvH;MAEAxF,IAAI,IAAI0I,KAAK,CAACpB,MAAM,GAAG9B,OAAO;IAChC;IAEA,OAAOxF,IAAI;EACb,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgI,YAAYA,CAAEtL,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACE,OAAO,CAAC,4BAA4B,EAAE,MAAM,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmL,WAAWA,CAAEP,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAAC5K,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmM,UAAUA,CAAEC,EAAE,EAAEhK,IAAI,EAAE;EAC7BgK,EAAE,CAAChK,IAAI,GAAGA,IAAI;EACd,OAAOgK,EAAE;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAST,KAAKA,CAAE5I,OAAO,EAAE;EACvB,OAAOA,OAAO,IAAIA,OAAO,CAACsJ,SAAS,GAAG,EAAE,GAAG,GAAG;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAElJ,IAAI,EAAEhB,IAAI,EAAE;EACnC;EACA,IAAImK,MAAM,GAAGnJ,IAAI,CAACoJ,MAAM,CAACC,KAAK,CAAC,WAAW,CAAC;EAE3C,IAAIF,MAAM,EAAE;IACV,KAAK,IAAI9H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8H,MAAM,CAAC1K,MAAM,EAAE4C,CAAC,EAAE,EAAE;MACtCrC,IAAI,CAACJ,IAAI,CAAC;QACRkB,IAAI,EAAEuB,CAAC;QACPiG,MAAM,EAAE,IAAI;QACZN,SAAS,EAAE,IAAI;QACfa,QAAQ,EAAE,KAAK;QACfD,MAAM,EAAE,KAAK;QACbD,OAAO,EAAE,KAAK;QACdD,QAAQ,EAAE,KAAK;QACfI,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF;EAEA,OAAOiB,UAAU,CAAC/I,IAAI,EAAEhB,IAAI,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsK,aAAaA,CAAEtJ,IAAI,EAAEhB,IAAI,EAAEW,OAAO,EAAE;EAC3C,IAAIrB,KAAK,GAAG,EAAE;EAEd,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,IAAI,CAACvB,MAAM,EAAE4C,CAAC,EAAE,EAAE;IACpC/C,KAAK,CAACM,IAAI,CAACuH,YAAY,CAACnG,IAAI,CAACqB,CAAC,CAAC,EAAErC,IAAI,EAAEW,OAAO,CAAC,CAACyJ,MAAM,CAAC;EACzD;EAEA,IAAIG,MAAM,GAAG,IAAI3C,MAAM,CAAC,KAAK,GAAGtI,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE6J,KAAK,CAAC5I,OAAO,CAAC,CAAC;EAEtE,OAAOoJ,UAAU,CAACQ,MAAM,EAAEvK,IAAI,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwK,cAAcA,CAAExJ,IAAI,EAAEhB,IAAI,EAAEW,OAAO,EAAE;EAC5C,OAAO+G,cAAc,CAACnJ,KAAK,CAACyC,IAAI,EAAEL,OAAO,CAAC,EAAEX,IAAI,EAAEW,OAAO,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+G,cAAcA,CAAEG,MAAM,EAAE7H,IAAI,EAAEW,OAAO,EAAE;EAC9C,IAAI,CAACmG,OAAO,CAAC9G,IAAI,CAAC,EAAE;IAClBW,OAAO,GAAG,sBAAwBX,IAAI,IAAIW,OAAQ;IAClDX,IAAI,GAAG,EAAE;EACX;EAEAW,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAI8J,MAAM,GAAG9J,OAAO,CAAC8J,MAAM;EAC3B,IAAIC,GAAG,GAAG/J,OAAO,CAAC+J,GAAG,KAAK,KAAK;EAC/B,IAAI7J,KAAK,GAAG,EAAE;;EAEd;EACA,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwF,MAAM,CAACpI,MAAM,EAAE4C,CAAC,EAAE,EAAE;IACtC,IAAIqH,KAAK,GAAG7B,MAAM,CAACxF,CAAC,CAAC;IAErB,IAAI,OAAOqH,KAAK,KAAK,QAAQ,EAAE;MAC7B7I,KAAK,IAAImI,YAAY,CAACU,KAAK,CAAC;IAC9B,CAAC,MAAM;MACL,IAAIpB,MAAM,GAAGU,YAAY,CAACU,KAAK,CAACpB,MAAM,CAAC;MACvC,IAAIC,OAAO,GAAG,KAAK,GAAGmB,KAAK,CAACZ,OAAO,GAAG,GAAG;MAEzC9I,IAAI,CAACJ,IAAI,CAAC8J,KAAK,CAAC;MAEhB,IAAIA,KAAK,CAACd,MAAM,EAAE;QAChBL,OAAO,IAAI,KAAK,GAAGD,MAAM,GAAGC,OAAO,GAAG,IAAI;MAC5C;MAEA,IAAImB,KAAK,CAACb,QAAQ,EAAE;QAClB,IAAI,CAACa,KAAK,CAACf,OAAO,EAAE;UAClBJ,OAAO,GAAG,KAAK,GAAGD,MAAM,GAAG,GAAG,GAAGC,OAAO,GAAG,KAAK;QAClD,CAAC,MAAM;UACLA,OAAO,GAAGD,MAAM,GAAG,GAAG,GAAGC,OAAO,GAAG,IAAI;QACzC;MACF,CAAC,MAAM;QACLA,OAAO,GAAGD,MAAM,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG;MACxC;MAEA1H,KAAK,IAAI0H,OAAO;IAClB;EACF;EAEA,IAAIP,SAAS,GAAGgB,YAAY,CAACrI,OAAO,CAACqH,SAAS,IAAI,GAAG,CAAC;EACtD,IAAI2C,iBAAiB,GAAG9J,KAAK,CAAC8F,KAAK,CAAC,CAACqB,SAAS,CAACvI,MAAM,CAAC,KAAKuI,SAAS;;EAEpE;EACA;EACA;EACA;EACA,IAAI,CAACyC,MAAM,EAAE;IACX5J,KAAK,GAAG,CAAC8J,iBAAiB,GAAG9J,KAAK,CAAC8F,KAAK,CAAC,CAAC,EAAE,CAACqB,SAAS,CAACvI,MAAM,CAAC,GAAGoB,KAAK,IAAI,KAAK,GAAGmH,SAAS,GAAG,SAAS;EACzG;EAEA,IAAI0C,GAAG,EAAE;IACP7J,KAAK,IAAI,GAAG;EACd,CAAC,MAAM;IACL;IACA;IACAA,KAAK,IAAI4J,MAAM,IAAIE,iBAAiB,GAAG,EAAE,GAAG,KAAK,GAAG3C,SAAS,GAAG,KAAK;EACvE;EAEA,OAAO+B,UAAU,CAAC,IAAInC,MAAM,CAAC,GAAG,GAAG/G,KAAK,EAAE0I,KAAK,CAAC5I,OAAO,CAAC,CAAC,EAAEX,IAAI,CAAC;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmH,YAAYA,CAAEnG,IAAI,EAAEhB,IAAI,EAAEW,OAAO,EAAE;EAC1C,IAAI,CAACmG,OAAO,CAAC9G,IAAI,CAAC,EAAE;IAClBW,OAAO,GAAG,sBAAwBX,IAAI,IAAIW,OAAQ;IAClDX,IAAI,GAAG,EAAE;EACX;EAEAW,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIK,IAAI,YAAY4G,MAAM,EAAE;IAC1B,OAAOsC,cAAc,CAAClJ,IAAI,EAAE,qBAAuBhB,IAAK,CAAC;EAC3D;EAEA,IAAI8G,OAAO,CAAC9F,IAAI,CAAC,EAAE;IACjB,OAAOsJ,aAAa,CAAC,qBAAuBtJ,IAAI,EAAG,qBAAuBhB,IAAI,EAAGW,OAAO,CAAC;EAC3F;EAEA,OAAO6J,cAAc,CAAC,qBAAuBxJ,IAAI,EAAG,qBAAuBhB,IAAI,EAAGW,OAAO,CAAC;AAC5F;AACAuG,cAAc,CAAC3I,KAAK,GAAG6I,OAAO;AAC9BF,cAAc,CAACI,OAAO,GAAGD,SAAS;AAClCH,cAAc,CAACM,gBAAgB,GAAGD,kBAAkB;AACpDL,cAAc,CAACQ,cAAc,GAAGD,gBAAgB;;AAEhD;;AAEA;AACA,IAAImD,kBAAkB,GAAG7K,MAAM,CAAC8K,MAAM,CAAC,IAAI,CAAC;AAE5C,SAASC,UAAUA,CACjB9J,IAAI,EACJE,MAAM,EACN6J,QAAQ,EACR;EACA7J,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAI;IACF,IAAI8J,MAAM,GACRJ,kBAAkB,CAAC5J,IAAI,CAAC,KACvB4J,kBAAkB,CAAC5J,IAAI,CAAC,GAAGkG,cAAc,CAACI,OAAO,CAACtG,IAAI,CAAC,CAAC;;IAE3D;IACA;IACA,IAAI,OAAOE,MAAM,CAAC+J,SAAS,KAAK,QAAQ,EAAE;MAAE/J,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC+J,SAAS;IAAE;IAE1E,OAAOD,MAAM,CAAC9J,MAAM,EAAE;MAAEuI,MAAM,EAAE;IAAK,CAAC,CAAC;EACzC,CAAC,CAAC,OAAO/K,CAAC,EAAE;IACV,IAAIV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACArB,IAAI,CAAC,OAAOqE,MAAM,CAAC+J,SAAS,KAAK,QAAQ,EAAG,oBAAoB,GAAGF,QAAQ,GAAG,IAAI,GAAIrM,CAAC,CAAC/B,OAAS,CAAC;IACpG;IACA,OAAO,EAAE;EACX,CAAC,SAAS;IACR;IACA,OAAOuE,MAAM,CAAC,CAAC,CAAC;EAClB;AACF;;AAEA;;AAEA,SAASgK,iBAAiBA,CACxBC,GAAG,EACHzI,OAAO,EACPwD,MAAM,EACNxF,MAAM,EACN;EACA,IAAI2H,IAAI,GAAG,OAAO8C,GAAG,KAAK,QAAQ,GAAG;IAAEnK,IAAI,EAAEmK;EAAI,CAAC,GAAGA,GAAG;EACxD;EACA,IAAI9C,IAAI,CAAC+C,WAAW,EAAE;IACpB,OAAO/C,IAAI;EACb,CAAC,MAAM,IAAIA,IAAI,CAACvH,IAAI,EAAE;IACpBuH,IAAI,GAAGtL,MAAM,CAAC,CAAC,CAAC,EAAEoO,GAAG,CAAC;IACtB,IAAIjK,MAAM,GAAGmH,IAAI,CAACnH,MAAM;IACxB,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MACxCmH,IAAI,CAACnH,MAAM,GAAGnE,MAAM,CAAC,CAAC,CAAC,EAAEmE,MAAM,CAAC;IAClC;IACA,OAAOmH,IAAI;EACb;;EAEA;EACA,IAAI,CAACA,IAAI,CAACrH,IAAI,IAAIqH,IAAI,CAACnH,MAAM,IAAIwB,OAAO,EAAE;IACxC2F,IAAI,GAAGtL,MAAM,CAAC,CAAC,CAAC,EAAEsL,IAAI,CAAC;IACvBA,IAAI,CAAC+C,WAAW,GAAG,IAAI;IACvB,IAAIC,QAAQ,GAAGtO,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC,EAAE2F,OAAO,CAACxB,MAAM,CAAC,EAAEmH,IAAI,CAACnH,MAAM,CAAC;IAC9D,IAAIwB,OAAO,CAAC5B,IAAI,EAAE;MAChBuH,IAAI,CAACvH,IAAI,GAAG4B,OAAO,CAAC5B,IAAI;MACxBuH,IAAI,CAACnH,MAAM,GAAGmK,QAAQ;IACxB,CAAC,MAAM,IAAI3I,OAAO,CAACrB,OAAO,CAAC5B,MAAM,EAAE;MACjC,IAAI6L,OAAO,GAAG5I,OAAO,CAACrB,OAAO,CAACqB,OAAO,CAACrB,OAAO,CAAC5B,MAAM,GAAG,CAAC,CAAC,CAACuB,IAAI;MAC9DqH,IAAI,CAACrH,IAAI,GAAG8J,UAAU,CAACQ,OAAO,EAAED,QAAQ,EAAG,OAAO,GAAI3I,OAAO,CAAC1B,IAAM,CAAC;IACvE,CAAC,MAAM,IAAIhD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MAChDrB,IAAI,CAAC,KAAK,EAAE,sDAAsD,CAAC;IACrE;IACA,OAAOwL,IAAI;EACb;EAEA,IAAIkD,UAAU,GAAG9E,SAAS,CAAC4B,IAAI,CAACrH,IAAI,IAAI,EAAE,CAAC;EAC3C,IAAIwK,QAAQ,GAAI9I,OAAO,IAAIA,OAAO,CAAC1B,IAAI,IAAK,GAAG;EAC/C,IAAIA,IAAI,GAAGuK,UAAU,CAACvK,IAAI,GACtB+E,WAAW,CAACwF,UAAU,CAACvK,IAAI,EAAEwK,QAAQ,EAAEtF,MAAM,IAAImC,IAAI,CAACnC,MAAM,CAAC,GAC7DsF,QAAQ;EAEZ,IAAIpN,KAAK,GAAGD,YAAY,CACtBoN,UAAU,CAACnN,KAAK,EAChBiK,IAAI,CAACjK,KAAK,EACVsC,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACnC,UAC3B,CAAC;EAED,IAAIyC,IAAI,GAAGoH,IAAI,CAACpH,IAAI,IAAIsK,UAAU,CAACtK,IAAI;EACvC,IAAIA,IAAI,IAAIA,IAAI,CAACmF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClCnF,IAAI,GAAG,GAAG,GAAGA,IAAI;EACnB;EAEA,OAAO;IACLmK,WAAW,EAAE,IAAI;IACjBpK,IAAI,EAAEA,IAAI;IACV5C,KAAK,EAAEA,KAAK;IACZ6C,IAAI,EAAEA;EACR,CAAC;AACH;;AAEA;;AAEA;AACA,IAAIwK,OAAO,GAAG,CAACzM,MAAM,EAAEe,MAAM,CAAC;AAC9B,IAAI2L,UAAU,GAAG,CAAC1M,MAAM,EAAEJ,KAAK,CAAC;AAEhC,IAAI+M,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;AAEzB,IAAIC,gBAAgB;AACpB,IAAIC,aAAa;AACjB,IAAIC,eAAe;AAEnB,IAAIC,IAAI,GAAG;EACTjL,IAAI,EAAE,YAAY;EAClByC,KAAK,EAAE;IACLyI,EAAE,EAAE;MACFxI,IAAI,EAAEiI,OAAO;MACbQ,QAAQ,EAAE;IACZ,CAAC;IACDC,GAAG,EAAE;MACH1I,IAAI,EAAExE,MAAM;MACZyE,OAAO,EAAE;IACX,CAAC;IACD0I,MAAM,EAAEC,OAAO;IACfC,KAAK,EAAED,OAAO;IACdE,SAAS,EAAEF,OAAO;IAClBlG,MAAM,EAAEkG,OAAO;IACfxO,OAAO,EAAEwO,OAAO;IAChBG,WAAW,EAAEvN,MAAM;IACnBwN,gBAAgB,EAAExN,MAAM;IACxByN,gBAAgB,EAAE;MAChBjJ,IAAI,EAAExE,MAAM;MACZyE,OAAO,EAAE;IACX,CAAC;IACDiJ,KAAK,EAAE;MACLlJ,IAAI,EAAEkI,UAAU;MAChBjI,OAAO,EAAE;IACX;EACF,CAAC;EACDC,MAAM,EAAE,SAASA,MAAMA,CAAEK,CAAC,EAAE;IAC1B,IAAI4I,QAAQ,GAAG,IAAI;IAEnB,IAAIjM,MAAM,GAAG,IAAI,CAACkM,OAAO;IACzB,IAAIlK,OAAO,GAAG,IAAI,CAACuB,MAAM;IACzB,IAAItC,GAAG,GAAGjB,MAAM,CAACmM,OAAO,CACtB,IAAI,CAACb,EAAE,EACPtJ,OAAO,EACP,IAAI,CAACwD,MACP,CAAC;IACD,IAAI1F,QAAQ,GAAGmB,GAAG,CAACnB,QAAQ;IAC3B,IAAIK,KAAK,GAAGc,GAAG,CAACd,KAAK;IACrB,IAAIiM,IAAI,GAAGnL,GAAG,CAACmL,IAAI;IAEnB,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,iBAAiB,GAAGtM,MAAM,CAACC,OAAO,CAACsM,eAAe;IACtD,IAAIC,sBAAsB,GAAGxM,MAAM,CAACC,OAAO,CAACwM,oBAAoB;IAChE;IACA,IAAIC,mBAAmB,GACrBJ,iBAAiB,IAAI,IAAI,GAAG,oBAAoB,GAAGA,iBAAiB;IACtE,IAAIK,wBAAwB,GAC1BH,sBAAsB,IAAI,IAAI,GAC1B,0BAA0B,GAC1BA,sBAAsB;IAC5B,IAAIX,WAAW,GACb,IAAI,CAACA,WAAW,IAAI,IAAI,GAAGa,mBAAmB,GAAG,IAAI,CAACb,WAAW;IACnE,IAAIC,gBAAgB,GAClB,IAAI,CAACA,gBAAgB,IAAI,IAAI,GACzBa,wBAAwB,GACxB,IAAI,CAACb,gBAAgB;IAE3B,IAAIc,aAAa,GAAGzM,KAAK,CAACJ,cAAc,GACpCH,WAAW,CAAC,IAAI,EAAE4K,iBAAiB,CAACrK,KAAK,CAACJ,cAAc,CAAC,EAAE,IAAI,EAAEC,MAAM,CAAC,GACxEG,KAAK;IAETkM,OAAO,CAACP,gBAAgB,CAAC,GAAG1K,WAAW,CAACY,OAAO,EAAE4K,aAAa,EAAE,IAAI,CAAChB,SAAS,CAAC;IAC/ES,OAAO,CAACR,WAAW,CAAC,GAAG,IAAI,CAACF,KAAK,IAAI,IAAI,CAACC,SAAS,GAC/CS,OAAO,CAACP,gBAAgB,CAAC,GACzB/J,eAAe,CAACC,OAAO,EAAE4K,aAAa,CAAC;IAE3C,IAAIb,gBAAgB,GAAGM,OAAO,CAACP,gBAAgB,CAAC,GAAG,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAE/E,IAAIc,OAAO,GAAG,SAAAA,CAAU7O,CAAC,EAAE;MACzB,IAAI8O,UAAU,CAAC9O,CAAC,CAAC,EAAE;QACjB,IAAIiO,QAAQ,CAAC/O,OAAO,EAAE;UACpB8C,MAAM,CAAC9C,OAAO,CAAC4C,QAAQ,EAAEmL,IAAI,CAAC;QAChC,CAAC,MAAM;UACLjL,MAAM,CAACd,IAAI,CAACY,QAAQ,EAAEmL,IAAI,CAAC;QAC7B;MACF;IACF,CAAC;IAED,IAAI8B,EAAE,GAAG;MAAEC,KAAK,EAAEF;IAAW,CAAC;IAC9B,IAAI5O,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC6N,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACA,KAAK,CAACtN,OAAO,CAAC,UAAUV,CAAC,EAAE;QAC9B+O,EAAE,CAAC/O,CAAC,CAAC,GAAG6O,OAAO;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLE,EAAE,CAAC,IAAI,CAACf,KAAK,CAAC,GAAGa,OAAO;IAC1B;IAEA,IAAI1J,IAAI,GAAG;MAAE8J,KAAK,EAAEZ;IAAQ,CAAC;IAE7B,IAAIa,UAAU,GACZ,CAAC,IAAI,CAACC,YAAY,CAACC,UAAU,IAC7B,IAAI,CAACD,YAAY,CAACpK,OAAO,IACzB,IAAI,CAACoK,YAAY,CAACpK,OAAO,CAAC;MACxBqJ,IAAI,EAAEA,IAAI;MACVjM,KAAK,EAAEA,KAAK;MACZkN,QAAQ,EAAER,OAAO;MACjBS,QAAQ,EAAEjB,OAAO,CAACR,WAAW,CAAC;MAC9B0B,aAAa,EAAElB,OAAO,CAACP,gBAAgB;IACzC,CAAC,CAAC;IAEJ,IAAIoB,UAAU,EAAE;MACd,IAAI5P,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC,IAAI,CAACiO,MAAM,EAAE;QACzD,CAACP,gBAAgB,IAAI/O,IAAI,CAAC,KAAK,EAAE,qMAAqM,CAAC;QACvO+O,gBAAgB,GAAG,IAAI;MACzB;MACA,IAAIgC,UAAU,CAACnO,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAOmO,UAAU,CAAC,CAAC,CAAC;MACtB,CAAC,MAAM,IAAIA,UAAU,CAACnO,MAAM,GAAG,CAAC,IAAI,CAACmO,UAAU,CAACnO,MAAM,EAAE;QACtD,IAAIzB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCrB,IAAI,CACF,KAAK,EACJ,0BAA0B,GAAI,IAAI,CAACmP,EAAG,GAAG,sHAC5C,CAAC;QACH;QACA,OAAO4B,UAAU,CAACnO,MAAM,KAAK,CAAC,GAAGsE,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE6J,UAAU,CAAC;MAClE;IACF;IAEA,IAAI5P,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,KAAK,IAAI,IAAI,CAACgQ,QAAQ,CAACC,SAAS,IAAI,CAACtC,aAAa,EAAE;QACtDhP,IAAI,CACF,KAAK,EACL,uNACF,CAAC;QACDgP,aAAa,GAAG,IAAI;MACtB;MACA,IAAI,OAAO,IAAI,IAAI,CAACqC,QAAQ,CAACC,SAAS,IAAI,CAACrC,eAAe,EAAE;QAC1DjP,IAAI,CACF,KAAK,EACL,yNACF,CAAC;QACDiP,eAAe,GAAG,IAAI;MACxB;IACF;IAEA,IAAI,IAAI,CAACI,GAAG,KAAK,GAAG,EAAE;MACpBrI,IAAI,CAAC4J,EAAE,GAAGA,EAAE;MACZ5J,IAAI,CAACgC,KAAK,GAAG;QAAEiH,IAAI,EAAEA,IAAI;QAAE,cAAc,EAAEL;MAAiB,CAAC;IAC/D,CAAC,MAAM;MACL;MACA,IAAIzP,CAAC,GAAGoR,UAAU,CAAC,IAAI,CAACC,MAAM,CAAC5K,OAAO,CAAC;MACvC,IAAIzG,CAAC,EAAE;QACL;QACAA,CAAC,CAACsR,QAAQ,GAAG,KAAK;QAClB,IAAIC,KAAK,GAAIvR,CAAC,CAAC6G,IAAI,GAAG9G,MAAM,CAAC,CAAC,CAAC,EAAEC,CAAC,CAAC6G,IAAI,CAAE;QACzC0K,KAAK,CAACd,EAAE,GAAGc,KAAK,CAACd,EAAE,IAAI,CAAC,CAAC;QACzB;QACA,KAAK,IAAIf,KAAK,IAAI6B,KAAK,CAACd,EAAE,EAAE;UAC1B,IAAIe,SAAS,GAAGD,KAAK,CAACd,EAAE,CAACf,KAAK,CAAC;UAC/B,IAAIA,KAAK,IAAIe,EAAE,EAAE;YACfc,KAAK,CAACd,EAAE,CAACf,KAAK,CAAC,GAAG9N,KAAK,CAACC,OAAO,CAAC2P,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;UACtE;QACF;QACA;QACA,KAAK,IAAIC,OAAO,IAAIhB,EAAE,EAAE;UACtB,IAAIgB,OAAO,IAAIF,KAAK,CAACd,EAAE,EAAE;YACvB;YACAc,KAAK,CAACd,EAAE,CAACgB,OAAO,CAAC,CAAC7O,IAAI,CAAC6N,EAAE,CAACgB,OAAO,CAAC,CAAC;UACrC,CAAC,MAAM;YACLF,KAAK,CAACd,EAAE,CAACgB,OAAO,CAAC,GAAGlB,OAAO;UAC7B;QACF;QAEA,IAAImB,MAAM,GAAI1R,CAAC,CAAC6G,IAAI,CAACgC,KAAK,GAAG9I,MAAM,CAAC,CAAC,CAAC,EAAEC,CAAC,CAAC6G,IAAI,CAACgC,KAAK,CAAE;QACtD6I,MAAM,CAAC5B,IAAI,GAAGA,IAAI;QAClB4B,MAAM,CAAC,cAAc,CAAC,GAAGjC,gBAAgB;MAC3C,CAAC,MAAM;QACL;QACA5I,IAAI,CAAC4J,EAAE,GAAGA,EAAE;MACd;IACF;IAEA,OAAO1J,CAAC,CAAC,IAAI,CAACmI,GAAG,EAAErI,IAAI,EAAE,IAAI,CAACwK,MAAM,CAAC5K,OAAO,CAAC;EAC/C;AACF,CAAC;AAED,SAAS+J,UAAUA,CAAE9O,CAAC,EAAE;EACtB;EACA,IAAIA,CAAC,CAACiQ,OAAO,IAAIjQ,CAAC,CAACkQ,MAAM,IAAIlQ,CAAC,CAACmQ,OAAO,IAAInQ,CAAC,CAACoQ,QAAQ,EAAE;IAAE;EAAO;EAC/D;EACA,IAAIpQ,CAAC,CAACqQ,gBAAgB,EAAE;IAAE;EAAO;EACjC;EACA,IAAIrQ,CAAC,CAACsQ,MAAM,KAAKrP,SAAS,IAAIjB,CAAC,CAACsQ,MAAM,KAAK,CAAC,EAAE;IAAE;EAAO;EACvD;EACA,IAAItQ,CAAC,CAACuQ,aAAa,IAAIvQ,CAAC,CAACuQ,aAAa,CAACC,YAAY,EAAE;IACnD,IAAIvM,MAAM,GAAGjE,CAAC,CAACuQ,aAAa,CAACC,YAAY,CAAC,QAAQ,CAAC;IACnD,IAAI,aAAa,CAACpF,IAAI,CAACnH,MAAM,CAAC,EAAE;MAAE;IAAO;EAC3C;EACA;EACA,IAAIjE,CAAC,CAACyQ,cAAc,EAAE;IACpBzQ,CAAC,CAACyQ,cAAc,CAAC,CAAC;EACpB;EACA,OAAO,IAAI;AACb;AAEA,SAASf,UAAUA,CAAExK,QAAQ,EAAE;EAC7B,IAAIA,QAAQ,EAAE;IACZ,IAAIwL,KAAK;IACT,KAAK,IAAI/M,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,QAAQ,CAACnE,MAAM,EAAE4C,CAAC,EAAE,EAAE;MACxC+M,KAAK,GAAGxL,QAAQ,CAACvB,CAAC,CAAC;MACnB,IAAI+M,KAAK,CAAClD,GAAG,KAAK,GAAG,EAAE;QACrB,OAAOkD,KAAK;MACd;MACA,IAAIA,KAAK,CAACxL,QAAQ,KAAKwL,KAAK,GAAGhB,UAAU,CAACgB,KAAK,CAACxL,QAAQ,CAAC,CAAC,EAAE;QAC1D,OAAOwL,KAAK;MACd;IACF;EACF;AACF;AAEA,IAAIC,IAAI;AAER,SAASC,OAAOA,CAAEC,GAAG,EAAE;EACrB,IAAID,OAAO,CAACE,SAAS,IAAIH,IAAI,KAAKE,GAAG,EAAE;IAAE;EAAO;EAChDD,OAAO,CAACE,SAAS,GAAG,IAAI;EAExBH,IAAI,GAAGE,GAAG;EAEV,IAAIE,KAAK,GAAG,SAAAA,CAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,KAAK/P,SAAS;EAAE,CAAC;EAEpD,IAAIgQ,gBAAgB,GAAG,SAAAA,CAAUtK,EAAE,EAAEuK,OAAO,EAAE;IAC5C,IAAIvN,CAAC,GAAGgD,EAAE,CAAC6I,QAAQ,CAAC2B,YAAY;IAChC,IAAIJ,KAAK,CAACpN,CAAC,CAAC,IAAIoN,KAAK,CAACpN,CAAC,GAAGA,CAAC,CAACwB,IAAI,CAAC,IAAI4L,KAAK,CAACpN,CAAC,GAAGA,CAAC,CAAC+C,qBAAqB,CAAC,EAAE;MACvE/C,CAAC,CAACgD,EAAE,EAAEuK,OAAO,CAAC;IAChB;EACF,CAAC;EAEDL,GAAG,CAACO,KAAK,CAAC;IACRC,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAI;MACrC,IAAIN,KAAK,CAAC,IAAI,CAACvB,QAAQ,CAACxN,MAAM,CAAC,EAAE;QAC/B,IAAI,CAAC4D,WAAW,GAAG,IAAI;QACvB,IAAI,CAAC0L,OAAO,GAAG,IAAI,CAAC9B,QAAQ,CAACxN,MAAM;QACnC,IAAI,CAACsP,OAAO,CAACtK,IAAI,CAAC,IAAI,CAAC;QACvB6J,GAAG,CAACU,IAAI,CAACC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAACF,OAAO,CAACG,OAAO,CAACzN,OAAO,CAAC;MACvE,CAAC,MAAM;QACL,IAAI,CAAC4B,WAAW,GAAI,IAAI,CAACM,OAAO,IAAI,IAAI,CAACA,OAAO,CAACN,WAAW,IAAK,IAAI;MACvE;MACAqL,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;IAC9B,CAAC;IACDS,SAAS,EAAE,SAASA,SAASA,CAAA,EAAI;MAC/BT,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC,CAAC;EAEF5P,MAAM,CAACsQ,cAAc,CAACd,GAAG,CAACvI,SAAS,EAAE,SAAS,EAAE;IAC9CsJ,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAI;MAAE,OAAO,IAAI,CAAChM,WAAW,CAAC0L,OAAO;IAAC;EACzD,CAAC,CAAC;EAEFjQ,MAAM,CAACsQ,cAAc,CAACd,GAAG,CAACvI,SAAS,EAAE,QAAQ,EAAE;IAC7CsJ,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAI;MAAE,OAAO,IAAI,CAAChM,WAAW,CAACiM,MAAM;IAAC;EACxD,CAAC,CAAC;EAEFhB,GAAG,CAACvK,SAAS,CAAC,YAAY,EAAE3B,IAAI,CAAC;EACjCkM,GAAG,CAACvK,SAAS,CAAC,YAAY,EAAE+G,IAAI,CAAC;EAEjC,IAAIyE,MAAM,GAAGjB,GAAG,CAACzJ,MAAM,CAAC2K,qBAAqB;EAC7C;EACAD,MAAM,CAACE,gBAAgB,GAAGF,MAAM,CAACG,gBAAgB,GAAGH,MAAM,CAACI,iBAAiB,GAAGJ,MAAM,CAACK,OAAO;AAC/F;;AAEA;;AAEA,IAAIC,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW;;AAE7C;;AAEA,SAASC,cAAcA,CACrBC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,WAAW,EACX;EACA;EACA,IAAIC,QAAQ,GAAGJ,WAAW,IAAI,EAAE;EAChC;EACA,IAAIK,OAAO,GAAGJ,UAAU,IAAIpR,MAAM,CAAC8K,MAAM,CAAC,IAAI,CAAC;EAC/C;EACA,IAAI2G,OAAO,GAAGJ,UAAU,IAAIrR,MAAM,CAAC8K,MAAM,CAAC,IAAI,CAAC;EAE/CoG,MAAM,CAAC7R,OAAO,CAAC,UAAUyB,KAAK,EAAE;IAC9B4Q,cAAc,CAACH,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAE3Q,KAAK,EAAEwQ,WAAW,CAAC;EAChE,CAAC,CAAC;;EAEF;EACA,KAAK,IAAIhP,CAAC,GAAG,CAAC,EAAEqP,CAAC,GAAGJ,QAAQ,CAAC7R,MAAM,EAAE4C,CAAC,GAAGqP,CAAC,EAAErP,CAAC,EAAE,EAAE;IAC/C,IAAIiP,QAAQ,CAACjP,CAAC,CAAC,KAAK,GAAG,EAAE;MACvBiP,QAAQ,CAAC1R,IAAI,CAAC0R,QAAQ,CAACK,MAAM,CAACtP,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvCqP,CAAC,EAAE;MACHrP,CAAC,EAAE;IACL;EACF;EAEA,IAAIrE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C;IACA,IAAI0T,KAAK,GAAGN;IACZ;IAAA,CACGnR,MAAM,CAAC,UAAUa,IAAI,EAAE;MAAE,OAAOA,IAAI,IAAIA,IAAI,CAACoF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIpF,IAAI,CAACoF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;IAAE,CAAC,CAAC;IAE/F,IAAIwL,KAAK,CAACnS,MAAM,GAAG,CAAC,EAAE;MACpB,IAAIoS,SAAS,GAAGD,KAAK,CAAC9S,GAAG,CAAC,UAAUkC,IAAI,EAAE;QAAE,OAAQ,IAAI,GAAGA,IAAI;MAAG,CAAC,CAAC,CAACtB,IAAI,CAAC,IAAI,CAAC;MAC/E7C,IAAI,CAAC,KAAK,EAAG,wFAAwF,GAAGgV,SAAU,CAAC;IACrH;EACF;EAEA,OAAO;IACLP,QAAQ,EAAEA,QAAQ;IAClBC,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA;EACX,CAAC;AACH;AAEA,SAASC,cAAcA,CACrBH,QAAQ,EACRC,OAAO,EACPC,OAAO,EACP3Q,KAAK,EACLa,MAAM,EACNoQ,OAAO,EACP;EACA,IAAI9Q,IAAI,GAAGH,KAAK,CAACG,IAAI;EACrB,IAAIF,IAAI,GAAGD,KAAK,CAACC,IAAI;EACrB,IAAI9C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCzB,MAAM,CAACuE,IAAI,IAAI,IAAI,EAAE,gDAAgD,CAAC;IACtEvE,MAAM,CACJ,OAAOoE,KAAK,CAACmE,SAAS,KAAK,QAAQ,EACnC,uCAAuC,GAAIhG,MAAM,CAC/CgC,IAAI,IAAIF,IACV,CAAE,GAAG,eAAe,GAAG,6CACzB,CAAC;IAEDjE,IAAI;IACF;IACA,CAAC,mBAAmB,CAACiN,IAAI,CAAC9I,IAAI,CAAC,EAC/B,oBAAoB,GAAGA,IAAI,GAAG,8CAA8C,GAC1E,sEAAsE,GACtE,mDACJ,CAAC;EACH;EAEA,IAAI+Q,mBAAmB,GACrBlR,KAAK,CAACkR,mBAAmB,IAAI,CAAC,CAAC;EACjC,IAAIC,cAAc,GAAGC,aAAa,CAACjR,IAAI,EAAEU,MAAM,EAAEqQ,mBAAmB,CAACtH,MAAM,CAAC;EAE5E,IAAI,OAAO5J,KAAK,CAACqR,aAAa,KAAK,SAAS,EAAE;IAC5CH,mBAAmB,CAAC9H,SAAS,GAAGpJ,KAAK,CAACqR,aAAa;EACrD;EAEA,IAAI3R,MAAM,GAAG;IACXS,IAAI,EAAEgR,cAAc;IACpBG,KAAK,EAAEC,iBAAiB,CAACJ,cAAc,EAAED,mBAAmB,CAAC;IAC7D5M,UAAU,EAAEtE,KAAK,CAACsE,UAAU,IAAI;MAAE1B,OAAO,EAAE5C,KAAK,CAACmE;IAAU,CAAC;IAC5DqN,KAAK,EAAExR,KAAK,CAACwR,KAAK,GACd,OAAOxR,KAAK,CAACwR,KAAK,KAAK,QAAQ,GAC7B,CAACxR,KAAK,CAACwR,KAAK,CAAC,GACbxR,KAAK,CAACwR,KAAK,GACb,EAAE;IACNtP,SAAS,EAAE,CAAC,CAAC;IACbG,UAAU,EAAE,CAAC,CAAC;IACdpC,IAAI,EAAEA,IAAI;IACVY,MAAM,EAAEA,MAAM;IACdoQ,OAAO,EAAEA,OAAO;IAChBQ,QAAQ,EAAEzR,KAAK,CAACyR,QAAQ;IACxBC,WAAW,EAAE1R,KAAK,CAAC0R,WAAW;IAC9BxR,IAAI,EAAEF,KAAK,CAACE,IAAI,IAAI,CAAC,CAAC;IACtBwC,KAAK,EACH1C,KAAK,CAAC0C,KAAK,IAAI,IAAI,GACf,CAAC,CAAC,GACF1C,KAAK,CAACsE,UAAU,GACdtE,KAAK,CAAC0C,KAAK,GACX;MAAEE,OAAO,EAAE5C,KAAK,CAAC0C;IAAM;EACjC,CAAC;EAED,IAAI1C,KAAK,CAAC+C,QAAQ,EAAE;IAClB;IACA;IACA;IACA,IAAI5F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IACE2C,KAAK,CAACC,IAAI,IACV,CAACD,KAAK,CAACyR,QAAQ,IACfzR,KAAK,CAAC+C,QAAQ,CAAC4O,IAAI,CAAC,UAAUpD,KAAK,EAAE;QAAE,OAAO,OAAO,CAACtF,IAAI,CAACsF,KAAK,CAACpO,IAAI,CAAC;MAAE,CAAC,CAAC,EAC1E;QACAnE,IAAI,CACF,KAAK,EACL,eAAe,GAAIgE,KAAK,CAACC,IAAK,GAAG,+BAA+B,GAC9D,qDAAqD,GAAID,KAAK,CAACC,IAAK,GAAG,SAAS,GAChF,qEAAqE,GACrE,mEAAmE,GACnE,gBACJ,CAAC;MACH;IACF;IACAD,KAAK,CAAC+C,QAAQ,CAACxE,OAAO,CAAC,UAAUgQ,KAAK,EAAE;MACtC,IAAIqD,YAAY,GAAGX,OAAO,GACtBjL,SAAS,CAAEiL,OAAO,GAAG,GAAG,GAAI1C,KAAK,CAACpO,IAAM,CAAC,GACzCrB,SAAS;MACb8R,cAAc,CAACH,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEpC,KAAK,EAAE7O,MAAM,EAAEkS,YAAY,CAAC;IACzE,CAAC,CAAC;EACJ;EAEA,IAAI,CAAClB,OAAO,CAAChR,MAAM,CAACS,IAAI,CAAC,EAAE;IACzBsQ,QAAQ,CAAC1R,IAAI,CAACW,MAAM,CAACS,IAAI,CAAC;IAC1BuQ,OAAO,CAAChR,MAAM,CAACS,IAAI,CAAC,GAAGT,MAAM;EAC/B;EAEA,IAAIM,KAAK,CAACwR,KAAK,KAAK1S,SAAS,EAAE;IAC7B,IAAI+S,OAAO,GAAG9T,KAAK,CAACC,OAAO,CAACgC,KAAK,CAACwR,KAAK,CAAC,GAAGxR,KAAK,CAACwR,KAAK,GAAG,CAACxR,KAAK,CAACwR,KAAK,CAAC;IACtE,KAAK,IAAIhQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqQ,OAAO,CAACjT,MAAM,EAAE,EAAE4C,CAAC,EAAE;MACvC,IAAIgQ,KAAK,GAAGK,OAAO,CAACrQ,CAAC,CAAC;MACtB,IAAIrE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAImU,KAAK,KAAKrR,IAAI,EAAE;QAC3DnE,IAAI,CACF,KAAK,EACJ,oDAAoD,GAAGmE,IAAI,GAAG,uEACjE,CAAC;QACD;QACA;MACF;MAEA,IAAI2R,UAAU,GAAG;QACf3R,IAAI,EAAEqR,KAAK;QACXzO,QAAQ,EAAE/C,KAAK,CAAC+C;MAClB,CAAC;MACD6N,cAAc,CACZH,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPmB,UAAU,EACVjR,MAAM,EACNnB,MAAM,CAACS,IAAI,IAAI,GAAG,CAAC;MACrB,CAAC;IACH;EACF;EAEA,IAAIF,IAAI,EAAE;IACR,IAAI,CAAC0Q,OAAO,CAAC1Q,IAAI,CAAC,EAAE;MAClB0Q,OAAO,CAAC1Q,IAAI,CAAC,GAAGP,MAAM;IACxB,CAAC,MAAM,IAAIvC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC4T,OAAO,EAAE;MAC5DjV,IAAI,CACF,KAAK,EACL,qCAAqC,GACnC,YAAY,GAAGiE,IAAI,GAAG,cAAc,GAAIP,MAAM,CAACS,IAAK,GAAG,MAC3D,CAAC;IACH;EACF;AACF;AAEA,SAASoR,iBAAiBA,CACxBpR,IAAI,EACJ+Q,mBAAmB,EACnB;EACA,IAAII,KAAK,GAAGjL,cAAc,CAAClG,IAAI,EAAE,EAAE,EAAE+Q,mBAAmB,CAAC;EACzD,IAAI/T,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI8B,IAAI,GAAGD,MAAM,CAAC8K,MAAM,CAAC,IAAI,CAAC;IAC9BsH,KAAK,CAACnS,IAAI,CAACZ,OAAO,CAAC,UAAUlC,GAAG,EAAE;MAChCL,IAAI,CACF,CAACmD,IAAI,CAAC9C,GAAG,CAAC4D,IAAI,CAAC,EACd,6CAA6C,GAAGE,IAAI,GAAG,IAC1D,CAAC;MACDhB,IAAI,CAAC9C,GAAG,CAAC4D,IAAI,CAAC,GAAG,IAAI;IACvB,CAAC,CAAC;EACJ;EACA,OAAOqR,KAAK;AACd;AAEA,SAASF,aAAaA,CACpBjR,IAAI,EACJU,MAAM,EACN+I,MAAM,EACN;EACA,IAAI,CAACA,MAAM,EAAE;IAAEzJ,IAAI,GAAGA,IAAI,CAACpD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAAE;EAC/C,IAAIoD,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAAE,OAAOA,IAAI;EAAC;EACnC,IAAIU,MAAM,IAAI,IAAI,EAAE;IAAE,OAAOV,IAAI;EAAC;EAClC,OAAO6F,SAAS,CAAGnF,MAAM,CAACV,IAAI,GAAI,GAAG,GAAGA,IAAK,CAAC;AAChD;;AAEA;;AAIA,SAAS4R,aAAaA,CACpB3B,MAAM,EACNvQ,MAAM,EACN;EACA,IAAIiB,GAAG,GAAGqP,cAAc,CAACC,MAAM,CAAC;EAChC,IAAIK,QAAQ,GAAG3P,GAAG,CAAC2P,QAAQ;EAC3B,IAAIC,OAAO,GAAG5P,GAAG,CAAC4P,OAAO;EACzB,IAAIC,OAAO,GAAG7P,GAAG,CAAC6P,OAAO;EAEzB,SAASqB,SAASA,CAAE5B,MAAM,EAAE;IAC1BD,cAAc,CAACC,MAAM,EAAEK,QAAQ,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACpD;EAEA,SAASsB,QAAQA,CAAEC,aAAa,EAAElS,KAAK,EAAE;IACvC,IAAIa,MAAM,GAAI,OAAOqR,aAAa,KAAK,QAAQ,GAAIvB,OAAO,CAACuB,aAAa,CAAC,GAAGpT,SAAS;IACrF;IACAqR,cAAc,CAAC,CAACnQ,KAAK,IAAIkS,aAAa,CAAC,EAAEzB,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAE9P,MAAM,CAAC;;IAE5E;IACA,IAAIA,MAAM,IAAIA,MAAM,CAAC2Q,KAAK,CAAC5S,MAAM,EAAE;MACjCuR,cAAc;MACZ;MACAtP,MAAM,CAAC2Q,KAAK,CAACvT,GAAG,CAAC,UAAUuT,KAAK,EAAE;QAAE,OAAQ;UAAErR,IAAI,EAAEqR,KAAK;UAAEzO,QAAQ,EAAE,CAAC/C,KAAK;QAAE,CAAC;MAAG,CAAC,CAAC,EACnFyQ,QAAQ,EACRC,OAAO,EACPC,OAAO,EACP9P,MACF,CAAC;IACH;EACF;EAEA,SAASsR,SAASA,CAAA,EAAI;IACpB,OAAO1B,QAAQ,CAACxS,GAAG,CAAC,UAAUkC,IAAI,EAAE;MAAE,OAAOuQ,OAAO,CAACvQ,IAAI,CAAC;IAAE,CAAC,CAAC;EAChE;EAEA,SAASqJ,KAAKA,CACZc,GAAG,EACH8H,YAAY,EACZxS,cAAc,EACd;IACA,IAAID,QAAQ,GAAG0K,iBAAiB,CAACC,GAAG,EAAE8H,YAAY,EAAE,KAAK,EAAEvS,MAAM,CAAC;IAClE,IAAII,IAAI,GAAGN,QAAQ,CAACM,IAAI;IAExB,IAAIA,IAAI,EAAE;MACR,IAAIP,MAAM,GAAGiR,OAAO,CAAC1Q,IAAI,CAAC;MAC1B,IAAI9C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCrB,IAAI,CAAC0D,MAAM,EAAG,mBAAmB,GAAGO,IAAI,GAAG,kBAAmB,CAAC;MACjE;MACA,IAAI,CAACP,MAAM,EAAE;QAAE,OAAO2S,YAAY,CAAC,IAAI,EAAE1S,QAAQ,CAAC;MAAC;MACnD,IAAI2S,UAAU,GAAG5S,MAAM,CAAC4R,KAAK,CAACnS,IAAI,CAC/BG,MAAM,CAAC,UAAUjD,GAAG,EAAE;QAAE,OAAO,CAACA,GAAG,CAAC2L,QAAQ;MAAE,CAAC,CAAC,CAChD/J,GAAG,CAAC,UAAU5B,GAAG,EAAE;QAAE,OAAOA,GAAG,CAAC4D,IAAI;MAAE,CAAC,CAAC;MAE3C,IAAI,OAAON,QAAQ,CAACU,MAAM,KAAK,QAAQ,EAAE;QACvCV,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;MACtB;MAEA,IAAI+R,YAAY,IAAI,OAAOA,YAAY,CAAC/R,MAAM,KAAK,QAAQ,EAAE;QAC3D,KAAK,IAAIhE,GAAG,IAAI+V,YAAY,CAAC/R,MAAM,EAAE;UACnC,IAAI,EAAEhE,GAAG,IAAIsD,QAAQ,CAACU,MAAM,CAAC,IAAIiS,UAAU,CAACvQ,OAAO,CAAC1F,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;YAC7DsD,QAAQ,CAACU,MAAM,CAAChE,GAAG,CAAC,GAAG+V,YAAY,CAAC/R,MAAM,CAAChE,GAAG,CAAC;UACjD;QACF;MACF;MAEAsD,QAAQ,CAACQ,IAAI,GAAG8J,UAAU,CAACvK,MAAM,CAACS,IAAI,EAAER,QAAQ,CAACU,MAAM,EAAG,gBAAgB,GAAGJ,IAAI,GAAG,IAAK,CAAC;MAC1F,OAAOoS,YAAY,CAAC3S,MAAM,EAAEC,QAAQ,EAAEC,cAAc,CAAC;IACvD,CAAC,MAAM,IAAID,QAAQ,CAACQ,IAAI,EAAE;MACxBR,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;MACpB,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiP,QAAQ,CAAC7R,MAAM,EAAE4C,CAAC,EAAE,EAAE;QACxC,IAAIrB,IAAI,GAAGsQ,QAAQ,CAACjP,CAAC,CAAC;QACtB,IAAI+Q,QAAQ,GAAG7B,OAAO,CAACvQ,IAAI,CAAC;QAC5B,IAAIqS,UAAU,CAACD,QAAQ,CAACjB,KAAK,EAAE3R,QAAQ,CAACQ,IAAI,EAAER,QAAQ,CAACU,MAAM,CAAC,EAAE;UAC9D,OAAOgS,YAAY,CAACE,QAAQ,EAAE5S,QAAQ,EAAEC,cAAc,CAAC;QACzD;MACF;IACF;IACA;IACA,OAAOyS,YAAY,CAAC,IAAI,EAAE1S,QAAQ,CAAC;EACrC;EAEA,SAAS8R,QAAQA,CACf/R,MAAM,EACNC,QAAQ,EACR;IACA,IAAI8S,gBAAgB,GAAG/S,MAAM,CAAC+R,QAAQ;IACtC,IAAIA,QAAQ,GAAG,OAAOgB,gBAAgB,KAAK,UAAU,GACjDA,gBAAgB,CAAChT,WAAW,CAACC,MAAM,EAAEC,QAAQ,EAAE,IAAI,EAAEE,MAAM,CAAC,CAAC,GAC7D4S,gBAAgB;IAEpB,IAAI,OAAOhB,QAAQ,KAAK,QAAQ,EAAE;MAChCA,QAAQ,GAAG;QAAEtR,IAAI,EAAEsR;MAAS,CAAC;IAC/B;IAEA,IAAI,CAACA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC7C,IAAItU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCrB,IAAI,CACF,KAAK,EAAG,2BAA2B,GAAI+M,IAAI,CAAC/H,SAAS,CAACyQ,QAAQ,CAChE,CAAC;MACH;MACA,OAAOY,YAAY,CAAC,IAAI,EAAE1S,QAAQ,CAAC;IACrC;IAEA,IAAIwJ,EAAE,GAAGsI,QAAQ;IACjB,IAAIxR,IAAI,GAAGkJ,EAAE,CAAClJ,IAAI;IAClB,IAAIE,IAAI,GAAGgJ,EAAE,CAAChJ,IAAI;IAClB,IAAI5C,KAAK,GAAGoC,QAAQ,CAACpC,KAAK;IAC1B,IAAI6C,IAAI,GAAGT,QAAQ,CAACS,IAAI;IACxB,IAAIC,MAAM,GAAGV,QAAQ,CAACU,MAAM;IAC5B9C,KAAK,GAAG4L,EAAE,CAACuJ,cAAc,CAAC,OAAO,CAAC,GAAGvJ,EAAE,CAAC5L,KAAK,GAAGA,KAAK;IACrD6C,IAAI,GAAG+I,EAAE,CAACuJ,cAAc,CAAC,MAAM,CAAC,GAAGvJ,EAAE,CAAC/I,IAAI,GAAGA,IAAI;IACjDC,MAAM,GAAG8I,EAAE,CAACuJ,cAAc,CAAC,QAAQ,CAAC,GAAGvJ,EAAE,CAAC9I,MAAM,GAAGA,MAAM;IAEzD,IAAIJ,IAAI,EAAE;MACR;MACA,IAAI0S,YAAY,GAAGhC,OAAO,CAAC1Q,IAAI,CAAC;MAChC,IAAI9C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCzB,MAAM,CAAC+W,YAAY,EAAG,iCAAiC,GAAG1S,IAAI,GAAG,eAAgB,CAAC;MACpF;MACA,OAAOuJ,KAAK,CAAC;QACXe,WAAW,EAAE,IAAI;QACjBtK,IAAI,EAAEA,IAAI;QACV1C,KAAK,EAAEA,KAAK;QACZ6C,IAAI,EAAEA,IAAI;QACVC,MAAM,EAAEA;MACV,CAAC,EAAEvB,SAAS,EAAEa,QAAQ,CAAC;IACzB,CAAC,MAAM,IAAIQ,IAAI,EAAE;MACf;MACA,IAAIsK,OAAO,GAAGmI,iBAAiB,CAACzS,IAAI,EAAET,MAAM,CAAC;MAC7C;MACA,IAAImT,YAAY,GAAG5I,UAAU,CAACQ,OAAO,EAAEpK,MAAM,EAAG,6BAA6B,GAAGoK,OAAO,GAAG,IAAK,CAAC;MAChG;MACA,OAAOjB,KAAK,CAAC;QACXe,WAAW,EAAE,IAAI;QACjBpK,IAAI,EAAE0S,YAAY;QAClBtV,KAAK,EAAEA,KAAK;QACZ6C,IAAI,EAAEA;MACR,CAAC,EAAEtB,SAAS,EAAEa,QAAQ,CAAC;IACzB,CAAC,MAAM;MACL,IAAIxC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCrB,IAAI,CAAC,KAAK,EAAG,2BAA2B,GAAI+M,IAAI,CAAC/H,SAAS,CAACyQ,QAAQ,CAAG,CAAC;MACzE;MACA,OAAOY,YAAY,CAAC,IAAI,EAAE1S,QAAQ,CAAC;IACrC;EACF;EAEA,SAAS6R,KAAKA,CACZ9R,MAAM,EACNC,QAAQ,EACRsR,OAAO,EACP;IACA,IAAI6B,WAAW,GAAG7I,UAAU,CAACgH,OAAO,EAAEtR,QAAQ,CAACU,MAAM,EAAG,4BAA4B,GAAG4Q,OAAO,GAAG,IAAK,CAAC;IACvG,IAAI8B,YAAY,GAAGvJ,KAAK,CAAC;MACvBe,WAAW,EAAE,IAAI;MACjBpK,IAAI,EAAE2S;IACR,CAAC,CAAC;IACF,IAAIC,YAAY,EAAE;MAChB,IAAIvS,OAAO,GAAGuS,YAAY,CAACvS,OAAO;MAClC,IAAIwS,aAAa,GAAGxS,OAAO,CAACA,OAAO,CAAC5B,MAAM,GAAG,CAAC,CAAC;MAC/Ce,QAAQ,CAACU,MAAM,GAAG0S,YAAY,CAAC1S,MAAM;MACrC,OAAOgS,YAAY,CAACW,aAAa,EAAErT,QAAQ,CAAC;IAC9C;IACA,OAAO0S,YAAY,CAAC,IAAI,EAAE1S,QAAQ,CAAC;EACrC;EAEA,SAAS0S,YAAYA,CACnB3S,MAAM,EACNC,QAAQ,EACRC,cAAc,EACd;IACA,IAAIF,MAAM,IAAIA,MAAM,CAAC+R,QAAQ,EAAE;MAC7B,OAAOA,QAAQ,CAAC/R,MAAM,EAAEE,cAAc,IAAID,QAAQ,CAAC;IACrD;IACA,IAAID,MAAM,IAAIA,MAAM,CAACuR,OAAO,EAAE;MAC5B,OAAOO,KAAK,CAAC9R,MAAM,EAAEC,QAAQ,EAAED,MAAM,CAACuR,OAAO,CAAC;IAChD;IACA,OAAOxR,WAAW,CAACC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,CAAC;EAC9D;EAEA,OAAO;IACL2J,KAAK,EAAEA,KAAK;IACZyI,QAAQ,EAAEA,QAAQ;IAClBE,SAAS,EAAEA,SAAS;IACpBH,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAASQ,UAAUA,CACjBlB,KAAK,EACLnR,IAAI,EACJE,MAAM,EACN;EACA,IAAIgH,CAAC,GAAGlH,IAAI,CAACqJ,KAAK,CAAC8H,KAAK,CAAC;EAEzB,IAAI,CAACjK,CAAC,EAAE;IACN,OAAO,KAAK;EACd,CAAC,MAAM,IAAI,CAAChH,MAAM,EAAE;IAClB,OAAO,IAAI;EACb;EAEA,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEyR,GAAG,GAAG5L,CAAC,CAACzI,MAAM,EAAE4C,CAAC,GAAGyR,GAAG,EAAE,EAAEzR,CAAC,EAAE;IAC5C,IAAInF,GAAG,GAAGiV,KAAK,CAACnS,IAAI,CAACqC,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAInF,GAAG,EAAE;MACP;MACAgE,MAAM,CAAChE,GAAG,CAAC4D,IAAI,IAAI,WAAW,CAAC,GAAG,OAAOoH,CAAC,CAAC7F,CAAC,CAAC,KAAK,QAAQ,GAAGxE,MAAM,CAACqK,CAAC,CAAC7F,CAAC,CAAC,CAAC,GAAG6F,CAAC,CAAC7F,CAAC,CAAC;IAClF;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAASoR,iBAAiBA,CAAEzS,IAAI,EAAET,MAAM,EAAE;EACxC,OAAOwF,WAAW,CAAC/E,IAAI,EAAET,MAAM,CAACmB,MAAM,GAAGnB,MAAM,CAACmB,MAAM,CAACV,IAAI,GAAG,GAAG,EAAE,IAAI,CAAC;AAC1E;;AAEA;;AAEA;AACA,IAAI+S,IAAI,GACNjD,SAAS,IAAIC,MAAM,CAACiD,WAAW,IAAIjD,MAAM,CAACiD,WAAW,CAACC,GAAG,GACrDlD,MAAM,CAACiD,WAAW,GAClBE,IAAI;AAEV,SAASC,WAAWA,CAAA,EAAI;EACtB,OAAOJ,IAAI,CAACE,GAAG,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC;AAC9B;AAEA,IAAIC,IAAI,GAAGF,WAAW,CAAC,CAAC;AAExB,SAASG,WAAWA,CAAA,EAAI;EACtB,OAAOD,IAAI;AACb;AAEA,SAASE,WAAWA,CAAErX,GAAG,EAAE;EACzB,OAAQmX,IAAI,GAAGnX,GAAG;AACpB;;AAEA;;AAEA,IAAIsX,aAAa,GAAGzU,MAAM,CAAC8K,MAAM,CAAC,IAAI,CAAC;AAEvC,SAAS4J,WAAWA,CAAA,EAAI;EACtB;EACA,IAAI,mBAAmB,IAAI1D,MAAM,CAACZ,OAAO,EAAE;IACzCY,MAAM,CAACZ,OAAO,CAACuE,iBAAiB,GAAG,QAAQ;EAC7C;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,eAAe,GAAG5D,MAAM,CAACvQ,QAAQ,CAACoU,QAAQ,GAAG,IAAI,GAAG7D,MAAM,CAACvQ,QAAQ,CAACqU,IAAI;EAC5E,IAAIC,YAAY,GAAG/D,MAAM,CAACvQ,QAAQ,CAACsM,IAAI,CAAClP,OAAO,CAAC+W,eAAe,EAAE,EAAE,CAAC;EACpE;EACA,IAAII,SAAS,GAAGhY,MAAM,CAAC,CAAC,CAAC,EAAEgU,MAAM,CAACZ,OAAO,CAAC6E,KAAK,CAAC;EAChDD,SAAS,CAAC7X,GAAG,GAAGoX,WAAW,CAAC,CAAC;EAC7BvD,MAAM,CAACZ,OAAO,CAAC8E,YAAY,CAACF,SAAS,EAAE,EAAE,EAAED,YAAY,CAAC;EACxD/D,MAAM,CAACmE,gBAAgB,CAAC,UAAU,EAAEC,cAAc,CAAC;EACnD,OAAO,YAAY;IACjBpE,MAAM,CAACqE,mBAAmB,CAAC,UAAU,EAAED,cAAc,CAAC;EACxD,CAAC;AACH;AAEA,SAASE,YAAYA,CACnB3U,MAAM,EACNsL,EAAE,EACFsJ,IAAI,EACJC,KAAK,EACL;EACA,IAAI,CAAC7U,MAAM,CAAC8U,GAAG,EAAE;IACf;EACF;EAEA,IAAIC,QAAQ,GAAG/U,MAAM,CAACC,OAAO,CAAC+U,cAAc;EAC5C,IAAI,CAACD,QAAQ,EAAE;IACb;EACF;EAEA,IAAIzX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCzB,MAAM,CAAC,OAAOgZ,QAAQ,KAAK,UAAU,EAAE,mCAAmC,CAAC;EAC7E;;EAEA;EACA/U,MAAM,CAAC8U,GAAG,CAACG,SAAS,CAAC,YAAY;IAC/B,IAAIC,QAAQ,GAAGC,iBAAiB,CAAC,CAAC;IAClC,IAAIC,YAAY,GAAGL,QAAQ,CAACxO,IAAI,CAC9BvG,MAAM,EACNsL,EAAE,EACFsJ,IAAI,EACJC,KAAK,GAAGK,QAAQ,GAAG,IACrB,CAAC;IAED,IAAI,CAACE,YAAY,EAAE;MACjB;IACF;IAEA,IAAI,OAAOA,YAAY,CAACC,IAAI,KAAK,UAAU,EAAE;MAC3CD,YAAY,CACTC,IAAI,CAAC,UAAUD,YAAY,EAAE;QAC5BE,gBAAgB,CAAEF,YAAY,EAAGF,QAAQ,CAAC;MAC5C,CAAC,CAAC,CACDK,KAAK,CAAC,UAAUlY,GAAG,EAAE;QACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCzB,MAAM,CAAC,KAAK,EAAEsB,GAAG,CAACR,QAAQ,CAAC,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC;IACN,CAAC,MAAM;MACLyY,gBAAgB,CAACF,YAAY,EAAEF,QAAQ,CAAC;IAC1C;EACF,CAAC,CAAC;AACJ;AAEA,SAASM,kBAAkBA,CAAA,EAAI;EAC7B,IAAIhZ,GAAG,GAAGoX,WAAW,CAAC,CAAC;EACvB,IAAIpX,GAAG,EAAE;IACPsX,aAAa,CAACtX,GAAG,CAAC,GAAG;MACnBkD,CAAC,EAAE2Q,MAAM,CAACoF,WAAW;MACrBC,CAAC,EAAErF,MAAM,CAACsF;IACZ,CAAC;EACH;AACF;AAEA,SAASlB,cAAcA,CAAEzW,CAAC,EAAE;EAC1BwX,kBAAkB,CAAC,CAAC;EACpB,IAAIxX,CAAC,CAACsW,KAAK,IAAItW,CAAC,CAACsW,KAAK,CAAC9X,GAAG,EAAE;IAC1BqX,WAAW,CAAC7V,CAAC,CAACsW,KAAK,CAAC9X,GAAG,CAAC;EAC1B;AACF;AAEA,SAAS2Y,iBAAiBA,CAAA,EAAI;EAC5B,IAAI3Y,GAAG,GAAGoX,WAAW,CAAC,CAAC;EACvB,IAAIpX,GAAG,EAAE;IACP,OAAOsX,aAAa,CAACtX,GAAG,CAAC;EAC3B;AACF;AAEA,SAASoZ,kBAAkBA,CAAEC,EAAE,EAAEnO,MAAM,EAAE;EACvC,IAAIoO,KAAK,GAAGC,QAAQ,CAACC,eAAe;EACpC,IAAIC,OAAO,GAAGH,KAAK,CAACI,qBAAqB,CAAC,CAAC;EAC3C,IAAIC,MAAM,GAAGN,EAAE,CAACK,qBAAqB,CAAC,CAAC;EACvC,OAAO;IACLxW,CAAC,EAAEyW,MAAM,CAACC,IAAI,GAAGH,OAAO,CAACG,IAAI,GAAG1O,MAAM,CAAChI,CAAC;IACxCgW,CAAC,EAAES,MAAM,CAACE,GAAG,GAAGJ,OAAO,CAACI,GAAG,GAAG3O,MAAM,CAACgO;EACvC,CAAC;AACH;AAEA,SAASY,eAAeA,CAAElX,GAAG,EAAE;EAC7B,OAAOmX,QAAQ,CAACnX,GAAG,CAACM,CAAC,CAAC,IAAI6W,QAAQ,CAACnX,GAAG,CAACsW,CAAC,CAAC;AAC3C;AAEA,SAASc,iBAAiBA,CAAEpX,GAAG,EAAE;EAC/B,OAAO;IACLM,CAAC,EAAE6W,QAAQ,CAACnX,GAAG,CAACM,CAAC,CAAC,GAAGN,GAAG,CAACM,CAAC,GAAG2Q,MAAM,CAACoF,WAAW;IAC/CC,CAAC,EAAEa,QAAQ,CAACnX,GAAG,CAACsW,CAAC,CAAC,GAAGtW,GAAG,CAACsW,CAAC,GAAGrF,MAAM,CAACsF;EACtC,CAAC;AACH;AAEA,SAASc,eAAeA,CAAErX,GAAG,EAAE;EAC7B,OAAO;IACLM,CAAC,EAAE6W,QAAQ,CAACnX,GAAG,CAACM,CAAC,CAAC,GAAGN,GAAG,CAACM,CAAC,GAAG,CAAC;IAC9BgW,CAAC,EAAEa,QAAQ,CAACnX,GAAG,CAACsW,CAAC,CAAC,GAAGtW,GAAG,CAACsW,CAAC,GAAG;EAC/B,CAAC;AACH;AAEA,SAASa,QAAQA,CAAEvH,CAAC,EAAE;EACpB,OAAO,OAAOA,CAAC,KAAK,QAAQ;AAC9B;AAEA,IAAI0H,sBAAsB,GAAG,MAAM;AAEnC,SAASpB,gBAAgBA,CAAEF,YAAY,EAAEF,QAAQ,EAAE;EACjD,IAAIyB,QAAQ,GAAG,OAAOvB,YAAY,KAAK,QAAQ;EAC/C,IAAIuB,QAAQ,IAAI,OAAOvB,YAAY,CAACwB,QAAQ,KAAK,QAAQ,EAAE;IACzD;IACA;IACA,IAAIf,EAAE,GAAGa,sBAAsB,CAACtN,IAAI,CAACgM,YAAY,CAACwB,QAAQ,CAAC,CAAC;IAAA,EACxDb,QAAQ,CAACc,cAAc,CAACzB,YAAY,CAACwB,QAAQ,CAAC3Q,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA,EACxD8P,QAAQ,CAACe,aAAa,CAAC1B,YAAY,CAACwB,QAAQ,CAAC;IAEjD,IAAIf,EAAE,EAAE;MACN,IAAInO,MAAM,GACR0N,YAAY,CAAC1N,MAAM,IAAI,OAAO0N,YAAY,CAAC1N,MAAM,KAAK,QAAQ,GAC1D0N,YAAY,CAAC1N,MAAM,GACnB,CAAC,CAAC;MACRA,MAAM,GAAG+O,eAAe,CAAC/O,MAAM,CAAC;MAChCwN,QAAQ,GAAGU,kBAAkB,CAACC,EAAE,EAAEnO,MAAM,CAAC;IAC3C,CAAC,MAAM,IAAI4O,eAAe,CAAClB,YAAY,CAAC,EAAE;MACxCF,QAAQ,GAAGsB,iBAAiB,CAACpB,YAAY,CAAC;IAC5C;EACF,CAAC,MAAM,IAAIuB,QAAQ,IAAIL,eAAe,CAAClB,YAAY,CAAC,EAAE;IACpDF,QAAQ,GAAGsB,iBAAiB,CAACpB,YAAY,CAAC;EAC5C;EAEA,IAAIF,QAAQ,EAAE;IACZ;IACA,IAAI,gBAAgB,IAAIa,QAAQ,CAACC,eAAe,CAACe,KAAK,EAAE;MACtD1G,MAAM,CAAC2G,QAAQ,CAAC;QACdZ,IAAI,EAAElB,QAAQ,CAACxV,CAAC;QAChB2W,GAAG,EAAEnB,QAAQ,CAACQ,CAAC;QACf;QACAX,QAAQ,EAAEK,YAAY,CAACL;MACzB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL1E,MAAM,CAAC2G,QAAQ,CAAC9B,QAAQ,CAACxV,CAAC,EAAEwV,QAAQ,CAACQ,CAAC,CAAC;IACzC;EACF;AACF;;AAEA;;AAEA,IAAIuB,iBAAiB,GACnB7G,SAAS,IACR,YAAY;EACX,IAAI8G,EAAE,GAAG7G,MAAM,CAAC8G,SAAS,CAACC,SAAS;EAEnC,IACE,CAACF,EAAE,CAAChV,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAIgV,EAAE,CAAChV,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,KACpEgV,EAAE,CAAChV,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAClCgV,EAAE,CAAChV,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAC3BgV,EAAE,CAAChV,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAClC;IACA,OAAO,KAAK;EACd;EAEA,OAAOmO,MAAM,CAACZ,OAAO,IAAI,OAAOY,MAAM,CAACZ,OAAO,CAAC4H,SAAS,KAAK,UAAU;AACzE,CAAC,CAAE,CAAC;AAEN,SAASA,SAASA,CAAEC,GAAG,EAAEpa,OAAO,EAAE;EAChCsY,kBAAkB,CAAC,CAAC;EACpB;EACA;EACA,IAAI/F,OAAO,GAAGY,MAAM,CAACZ,OAAO;EAC5B,IAAI;IACF,IAAIvS,OAAO,EAAE;MACX;MACA,IAAImX,SAAS,GAAGhY,MAAM,CAAC,CAAC,CAAC,EAAEoT,OAAO,CAAC6E,KAAK,CAAC;MACzCD,SAAS,CAAC7X,GAAG,GAAGoX,WAAW,CAAC,CAAC;MAC7BnE,OAAO,CAAC8E,YAAY,CAACF,SAAS,EAAE,EAAE,EAAEiD,GAAG,CAAC;IAC1C,CAAC,MAAM;MACL7H,OAAO,CAAC4H,SAAS,CAAC;QAAE7a,GAAG,EAAEqX,WAAW,CAACJ,WAAW,CAAC,CAAC;MAAE,CAAC,EAAE,EAAE,EAAE6D,GAAG,CAAC;IACjE;EACF,CAAC,CAAC,OAAOtZ,CAAC,EAAE;IACVqS,MAAM,CAACvQ,QAAQ,CAAC5C,OAAO,GAAG,SAAS,GAAG,QAAQ,CAAC,CAACoa,GAAG,CAAC;EACtD;AACF;AAEA,SAAS/C,YAAYA,CAAE+C,GAAG,EAAE;EAC1BD,SAAS,CAACC,GAAG,EAAE,IAAI,CAAC;AACtB;;AAEA;AACA,IAAIC,qBAAqB,GAAG;EAC1BC,UAAU,EAAE,CAAC;EACbC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE;AACd,CAAC;AAED,SAASC,+BAA+BA,CAAEhD,IAAI,EAAEtJ,EAAE,EAAE;EAClD,OAAOuM,iBAAiB,CACtBjD,IAAI,EACJtJ,EAAE,EACFiM,qBAAqB,CAACC,UAAU,EAC/B,+BAA+B,GAAI5C,IAAI,CAACnU,QAAS,GAAG,UAAU,GAAIqX,cAAc,CAC/ExM,EACF,CAAE,GAAG,4BACP,CAAC;AACH;AAEA,SAASyM,+BAA+BA,CAAEnD,IAAI,EAAEtJ,EAAE,EAAE;EAClD,IAAI0M,KAAK,GAAGH,iBAAiB,CAC3BjD,IAAI,EACJtJ,EAAE,EACFiM,qBAAqB,CAACI,UAAU,EAC/B,sDAAsD,GAAI/C,IAAI,CAACnU,QAAS,GAAG,KAC9E,CAAC;EACD;EACAuX,KAAK,CAAC5X,IAAI,GAAG,sBAAsB;EACnC,OAAO4X,KAAK;AACd;AAEA,SAASC,8BAA8BA,CAAErD,IAAI,EAAEtJ,EAAE,EAAE;EACjD,OAAOuM,iBAAiB,CACtBjD,IAAI,EACJtJ,EAAE,EACFiM,qBAAqB,CAACG,SAAS,EAC9B,8BAA8B,GAAI9C,IAAI,CAACnU,QAAS,GAAG,UAAU,GAAI6K,EAAE,CAAC7K,QAAS,GAAG,2BACnF,CAAC;AACH;AAEA,SAASyX,4BAA4BA,CAAEtD,IAAI,EAAEtJ,EAAE,EAAE;EAC/C,OAAOuM,iBAAiB,CACtBjD,IAAI,EACJtJ,EAAE,EACFiM,qBAAqB,CAACE,OAAO,EAC5B,4BAA4B,GAAI7C,IAAI,CAACnU,QAAS,GAAG,UAAU,GAAI6K,EAAE,CAAC7K,QAAS,GAAG,4BACjF,CAAC;AACH;AAEA,SAASoX,iBAAiBA,CAAEjD,IAAI,EAAEtJ,EAAE,EAAExI,IAAI,EAAE7G,OAAO,EAAE;EACnD,IAAI+b,KAAK,GAAG,IAAI9b,KAAK,CAACD,OAAO,CAAC;EAC9B+b,KAAK,CAACG,SAAS,GAAG,IAAI;EACtBH,KAAK,CAACpD,IAAI,GAAGA,IAAI;EACjBoD,KAAK,CAAC1M,EAAE,GAAGA,EAAE;EACb0M,KAAK,CAAClV,IAAI,GAAGA,IAAI;EAEjB,OAAOkV,KAAK;AACd;AAEA,IAAII,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;AAEjD,SAASN,cAAcA,CAAExM,EAAE,EAAE;EAC3B,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;IAAE,OAAOA,EAAE;EAAC;EACxC,IAAI,MAAM,IAAIA,EAAE,EAAE;IAAE,OAAOA,EAAE,CAAChL,IAAI;EAAC;EACnC,IAAIR,QAAQ,GAAG,CAAC,CAAC;EACjBsY,eAAe,CAAC1Z,OAAO,CAAC,UAAUlC,GAAG,EAAE;IACrC,IAAIA,GAAG,IAAI8O,EAAE,EAAE;MAAExL,QAAQ,CAACtD,GAAG,CAAC,GAAG8O,EAAE,CAAC9O,GAAG,CAAC;IAAE;EAC5C,CAAC,CAAC;EACF,OAAO0M,IAAI,CAAC/H,SAAS,CAACrB,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1C;AAEA,SAASuY,OAAOA,CAAEhb,GAAG,EAAE;EACrB,OAAOgC,MAAM,CAACiH,SAAS,CAACzJ,QAAQ,CAAC0J,IAAI,CAAClJ,GAAG,CAAC,CAAC6E,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAClE;AAEA,SAASoW,mBAAmBA,CAAEjb,GAAG,EAAEkb,SAAS,EAAE;EAC5C,OACEF,OAAO,CAAChb,GAAG,CAAC,IACZA,GAAG,CAAC8a,SAAS,KACZI,SAAS,IAAI,IAAI,IAAIlb,GAAG,CAACyF,IAAI,KAAKyV,SAAS,CAAC;AAEjD;;AAEA;;AAEA,SAASC,QAAQA,CAAEC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAChC,IAAIC,IAAI,GAAG,SAAAA,CAAUxR,KAAK,EAAE;IAC1B,IAAIA,KAAK,IAAIqR,KAAK,CAAC1Z,MAAM,EAAE;MACzB4Z,EAAE,CAAC,CAAC;IACN,CAAC,MAAM;MACL,IAAIF,KAAK,CAACrR,KAAK,CAAC,EAAE;QAChBsR,EAAE,CAACD,KAAK,CAACrR,KAAK,CAAC,EAAE,YAAY;UAC3BwR,IAAI,CAACxR,KAAK,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLwR,IAAI,CAACxR,KAAK,GAAG,CAAC,CAAC;MACjB;IACF;EACF,CAAC;EACDwR,IAAI,CAAC,CAAC,CAAC;AACT;;AAEA;;AAEA,SAASC,sBAAsBA,CAAElY,OAAO,EAAE;EACxC,OAAO,UAAU2K,EAAE,EAAEsJ,IAAI,EAAEjN,IAAI,EAAE;IAC/B,IAAImR,QAAQ,GAAG,KAAK;IACpB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIf,KAAK,GAAG,IAAI;IAEhBgB,iBAAiB,CAACrY,OAAO,EAAE,UAAUsY,GAAG,EAAEhW,CAAC,EAAE0G,KAAK,EAAEnN,GAAG,EAAE;MACvD;MACA;MACA;MACA;MACA;MACA,IAAI,OAAOyc,GAAG,KAAK,UAAU,IAAIA,GAAG,CAACC,GAAG,KAAKja,SAAS,EAAE;QACtD6Z,QAAQ,GAAG,IAAI;QACfC,OAAO,EAAE;QAET,IAAI5M,OAAO,GAAGgN,IAAI,CAAC,UAAUC,WAAW,EAAE;UACxC,IAAIC,UAAU,CAACD,WAAW,CAAC,EAAE;YAC3BA,WAAW,GAAGA,WAAW,CAACrW,OAAO;UACnC;UACA;UACAkW,GAAG,CAACK,QAAQ,GAAG,OAAOF,WAAW,KAAK,UAAU,GAC5CA,WAAW,GACXzK,IAAI,CAACtS,MAAM,CAAC+c,WAAW,CAAC;UAC5BzP,KAAK,CAAClF,UAAU,CAACjI,GAAG,CAAC,GAAG4c,WAAW;UACnCL,OAAO,EAAE;UACT,IAAIA,OAAO,IAAI,CAAC,EAAE;YAChBpR,IAAI,CAAC,CAAC;UACR;QACF,CAAC,CAAC;QAEF,IAAI4R,MAAM,GAAGJ,IAAI,CAAC,UAAUK,MAAM,EAAE;UAClC,IAAIC,GAAG,GAAG,oCAAoC,GAAGjd,GAAG,GAAG,IAAI,GAAGgd,MAAM;UACpElc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIrB,IAAI,CAAC,KAAK,EAAEsd,GAAG,CAAC;UACzD,IAAI,CAACzB,KAAK,EAAE;YACVA,KAAK,GAAGK,OAAO,CAACmB,MAAM,CAAC,GACnBA,MAAM,GACN,IAAItd,KAAK,CAACud,GAAG,CAAC;YAClB9R,IAAI,CAACqQ,KAAK,CAAC;UACb;QACF,CAAC,CAAC;QAEF,IAAIzZ,GAAG;QACP,IAAI;UACFA,GAAG,GAAG0a,GAAG,CAAC9M,OAAO,EAAEoN,MAAM,CAAC;QAC5B,CAAC,CAAC,OAAOvb,CAAC,EAAE;UACVub,MAAM,CAACvb,CAAC,CAAC;QACX;QACA,IAAIO,GAAG,EAAE;UACP,IAAI,OAAOA,GAAG,CAAC8W,IAAI,KAAK,UAAU,EAAE;YAClC9W,GAAG,CAAC8W,IAAI,CAAClJ,OAAO,EAAEoN,MAAM,CAAC;UAC3B,CAAC,MAAM;YACL;YACA,IAAIG,IAAI,GAAGnb,GAAG,CAAC+F,SAAS;YACxB,IAAIoV,IAAI,IAAI,OAAOA,IAAI,CAACrE,IAAI,KAAK,UAAU,EAAE;cAC3CqE,IAAI,CAACrE,IAAI,CAAClJ,OAAO,EAAEoN,MAAM,CAAC;YAC5B;UACF;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACT,QAAQ,EAAE;MAAEnR,IAAI,CAAC,CAAC;IAAE;EAC3B,CAAC;AACH;AAEA,SAASqR,iBAAiBA,CACxBrY,OAAO,EACP+X,EAAE,EACF;EACA,OAAOiB,OAAO,CAAChZ,OAAO,CAACvC,GAAG,CAAC,UAAUoJ,CAAC,EAAE;IACtC,OAAOnI,MAAM,CAACC,IAAI,CAACkI,CAAC,CAAC/C,UAAU,CAAC,CAACrG,GAAG,CAAC,UAAU5B,GAAG,EAAE;MAAE,OAAOkc,EAAE,CAC7DlR,CAAC,CAAC/C,UAAU,CAACjI,GAAG,CAAC,EACjBgL,CAAC,CAACnF,SAAS,CAAC7F,GAAG,CAAC,EAChBgL,CAAC,EAAEhL,GACL,CAAC;IAAE,CAAC,CAAC;EACP,CAAC,CAAC,CAAC;AACL;AAEA,SAASmd,OAAOA,CAAEtT,GAAG,EAAE;EACrB,OAAOnI,KAAK,CAACoI,SAAS,CAACsT,MAAM,CAACC,KAAK,CAAC,EAAE,EAAExT,GAAG,CAAC;AAC9C;AAEA,IAAIyT,SAAS,GACX,OAAOC,MAAM,KAAK,UAAU,IAC5B,OAAOA,MAAM,CAACC,WAAW,KAAK,QAAQ;AAExC,SAASX,UAAUA,CAAEja,GAAG,EAAE;EACxB,OAAOA,GAAG,CAAC6a,UAAU,IAAKH,SAAS,IAAI1a,GAAG,CAAC2a,MAAM,CAACC,WAAW,CAAC,KAAK,QAAS;AAC9E;;AAEA;AACA;AACA;AACA;AACA,SAASb,IAAIA,CAAET,EAAE,EAAE;EACjB,IAAIwB,MAAM,GAAG,KAAK;EAClB,OAAO,YAAY;IACjB,IAAIC,IAAI,GAAG,EAAE;MAAE/G,GAAG,GAAGgH,SAAS,CAACrb,MAAM;IACrC,OAAQqU,GAAG,EAAE,EAAG+G,IAAI,CAAE/G,GAAG,CAAE,GAAGgH,SAAS,CAAEhH,GAAG,CAAE;IAE9C,IAAI8G,MAAM,EAAE;MAAE;IAAO;IACrBA,MAAM,GAAG,IAAI;IACb,OAAOxB,EAAE,CAACmB,KAAK,CAAC,IAAI,EAAEM,IAAI,CAAC;EAC7B,CAAC;AACH;;AAEA;;AAEA,IAAIE,OAAO,GAAG,SAASA,OAAOA,CAAEra,MAAM,EAAEuF,IAAI,EAAE;EAC5C,IAAI,CAACvF,MAAM,GAAGA,MAAM;EACpB,IAAI,CAACuF,IAAI,GAAG+U,aAAa,CAAC/U,IAAI,CAAC;EAC/B;EACA,IAAI,CAACvD,OAAO,GAAGlB,KAAK;EACpB,IAAI,CAACiY,OAAO,GAAG,IAAI;EACnB,IAAI,CAACwB,KAAK,GAAG,KAAK;EAClB,IAAI,CAACC,QAAQ,GAAG,EAAE;EAClB,IAAI,CAACC,aAAa,GAAG,EAAE;EACvB,IAAI,CAACC,QAAQ,GAAG,EAAE;EAClB,IAAI,CAACC,SAAS,GAAG,EAAE;AACrB,CAAC;AAEDN,OAAO,CAAC/T,SAAS,CAACsU,MAAM,GAAG,SAASA,MAAMA,CAAEjC,EAAE,EAAE;EAC9C,IAAI,CAACA,EAAE,GAAGA,EAAE;AACd,CAAC;AAED0B,OAAO,CAAC/T,SAAS,CAACuU,OAAO,GAAG,SAASA,OAAOA,CAAElC,EAAE,EAAEmC,OAAO,EAAE;EACzD,IAAI,IAAI,CAACP,KAAK,EAAE;IACd5B,EAAE,CAAC,CAAC;EACN,CAAC,MAAM;IACL,IAAI,CAAC6B,QAAQ,CAACtb,IAAI,CAACyZ,EAAE,CAAC;IACtB,IAAImC,OAAO,EAAE;MACX,IAAI,CAACL,aAAa,CAACvb,IAAI,CAAC4b,OAAO,CAAC;IAClC;EACF;AACF,CAAC;AAEDT,OAAO,CAAC/T,SAAS,CAACyU,OAAO,GAAG,SAASA,OAAOA,CAAED,OAAO,EAAE;EACrD,IAAI,CAACJ,QAAQ,CAACxb,IAAI,CAAC4b,OAAO,CAAC;AAC7B,CAAC;AAEDT,OAAO,CAAC/T,SAAS,CAAC0U,YAAY,GAAG,SAASA,YAAYA,CACpDlb,QAAQ,EACRmb,UAAU,EACVC,OAAO,EACP;EACE,IAAIjP,QAAQ,GAAG,IAAI;EAErB,IAAI9L,KAAK;EACT;EACA,IAAI;IACFA,KAAK,GAAG,IAAI,CAACH,MAAM,CAAC2J,KAAK,CAAC7J,QAAQ,EAAE,IAAI,CAACkC,OAAO,CAAC;EACnD,CAAC,CAAC,OAAOhE,CAAC,EAAE;IACV,IAAI,CAAC0c,QAAQ,CAAChc,OAAO,CAAC,UAAUia,EAAE,EAAE;MAClCA,EAAE,CAAC3a,CAAC,CAAC;IACP,CAAC,CAAC;IACF;IACA,MAAMA,CAAC;EACT;EACA,IAAImd,IAAI,GAAG,IAAI,CAACnZ,OAAO;EACvB,IAAI,CAACoZ,iBAAiB,CACpBjb,KAAK,EACL,YAAY;IACV8L,QAAQ,CAACoP,WAAW,CAAClb,KAAK,CAAC;IAC3B8a,UAAU,IAAIA,UAAU,CAAC9a,KAAK,CAAC;IAC/B8L,QAAQ,CAACqP,SAAS,CAAC,CAAC;IACpBrP,QAAQ,CAACjM,MAAM,CAACub,UAAU,CAAC7c,OAAO,CAAC,UAAUkG,IAAI,EAAE;MACjDA,IAAI,IAAIA,IAAI,CAACzE,KAAK,EAAEgb,IAAI,CAAC;IAC3B,CAAC,CAAC;;IAEF;IACA,IAAI,CAAClP,QAAQ,CAACsO,KAAK,EAAE;MACnBtO,QAAQ,CAACsO,KAAK,GAAG,IAAI;MACrBtO,QAAQ,CAACuO,QAAQ,CAAC9b,OAAO,CAAC,UAAUia,EAAE,EAAE;QACtCA,EAAE,CAACxY,KAAK,CAAC;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EACD,UAAU9C,GAAG,EAAE;IACb,IAAI6d,OAAO,EAAE;MACXA,OAAO,CAAC7d,GAAG,CAAC;IACd;IACA,IAAIA,GAAG,IAAI,CAAC4O,QAAQ,CAACsO,KAAK,EAAE;MAC1B;MACA;MACA;MACA;MACA,IAAI,CAACjC,mBAAmB,CAACjb,GAAG,EAAEka,qBAAqB,CAACC,UAAU,CAAC,IAAI2D,IAAI,KAAKra,KAAK,EAAE;QACjFmL,QAAQ,CAACsO,KAAK,GAAG,IAAI;QACrBtO,QAAQ,CAACwO,aAAa,CAAC/b,OAAO,CAAC,UAAUia,EAAE,EAAE;UAC3CA,EAAE,CAACtb,GAAG,CAAC;QACT,CAAC,CAAC;MACJ;IACF;EACF,CACF,CAAC;AACH,CAAC;AAEDgd,OAAO,CAAC/T,SAAS,CAAC8U,iBAAiB,GAAG,SAASA,iBAAiBA,CAAEjb,KAAK,EAAE8a,UAAU,EAAEC,OAAO,EAAE;EAC1F,IAAIjP,QAAQ,GAAG,IAAI;EAErB,IAAIjK,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAI,CAAC+W,OAAO,GAAG5Y,KAAK;EACpB,IAAIqb,KAAK,GAAG,SAAAA,CAAUne,GAAG,EAAE;IACzB;IACA;IACA;IACA,IAAI,CAACib,mBAAmB,CAACjb,GAAG,CAAC,IAAIgb,OAAO,CAAChb,GAAG,CAAC,EAAE;MAC7C,IAAI4O,QAAQ,CAACyO,QAAQ,CAAC3b,MAAM,EAAE;QAC5BkN,QAAQ,CAACyO,QAAQ,CAAChc,OAAO,CAAC,UAAUia,EAAE,EAAE;UACtCA,EAAE,CAACtb,GAAG,CAAC;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCrB,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;QACxD;QACAC,OAAO,CAAC4b,KAAK,CAAC3a,GAAG,CAAC;MACpB;IACF;IACA6d,OAAO,IAAIA,OAAO,CAAC7d,GAAG,CAAC;EACzB,CAAC;EACD,IAAIoe,cAAc,GAAGtb,KAAK,CAACQ,OAAO,CAAC5B,MAAM,GAAG,CAAC;EAC7C,IAAI2c,gBAAgB,GAAG1Z,OAAO,CAACrB,OAAO,CAAC5B,MAAM,GAAG,CAAC;EACjD,IACEqC,WAAW,CAACjB,KAAK,EAAE6B,OAAO,CAAC;EAC3B;EACAyZ,cAAc,KAAKC,gBAAgB,IACnCvb,KAAK,CAACQ,OAAO,CAAC8a,cAAc,CAAC,KAAKzZ,OAAO,CAACrB,OAAO,CAAC+a,gBAAgB,CAAC,EACnE;IACA,IAAI,CAACJ,SAAS,CAAC,CAAC;IAChB,IAAInb,KAAK,CAACI,IAAI,EAAE;MACdoU,YAAY,CAAC,IAAI,CAAC3U,MAAM,EAAEgC,OAAO,EAAE7B,KAAK,EAAE,KAAK,CAAC;IAClD;IACA,OAAOqb,KAAK,CAACzD,+BAA+B,CAAC/V,OAAO,EAAE7B,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIc,GAAG,GAAG0a,YAAY,CACpB,IAAI,CAAC3Z,OAAO,CAACrB,OAAO,EACpBR,KAAK,CAACQ,OACR,CAAC;EACC,IAAIib,OAAO,GAAG3a,GAAG,CAAC2a,OAAO;EACzB,IAAIC,WAAW,GAAG5a,GAAG,CAAC4a,WAAW;EACjC,IAAIC,SAAS,GAAG7a,GAAG,CAAC6a,SAAS;EAE/B,IAAIrD,KAAK,GAAG,EAAE,CAACmB,MAAM;EACnB;EACAmC,kBAAkB,CAACF,WAAW,CAAC;EAC/B;EACA,IAAI,CAAC7b,MAAM,CAACgc,WAAW;EACvB;EACAC,kBAAkB,CAACL,OAAO,CAAC;EAC3B;EACAE,SAAS,CAAC1d,GAAG,CAAC,UAAUoJ,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACqK,WAAW;EAAE,CAAC,CAAC;EACrD;EACAgH,sBAAsB,CAACiD,SAAS,CAClC,CAAC;EAED,IAAII,QAAQ,GAAG,SAAAA,CAAUtX,IAAI,EAAE+C,IAAI,EAAE;IACnC,IAAIsE,QAAQ,CAAC8M,OAAO,KAAK5Y,KAAK,EAAE;MAC9B,OAAOqb,KAAK,CAACvD,8BAA8B,CAACjW,OAAO,EAAE7B,KAAK,CAAC,CAAC;IAC9D;IACA,IAAI;MACFyE,IAAI,CAACzE,KAAK,EAAE6B,OAAO,EAAE,UAAUsJ,EAAE,EAAE;QACjC,IAAIA,EAAE,KAAK,KAAK,EAAE;UAChB;UACAW,QAAQ,CAACqP,SAAS,CAAC,IAAI,CAAC;UACxBE,KAAK,CAACtD,4BAA4B,CAAClW,OAAO,EAAE7B,KAAK,CAAC,CAAC;QACrD,CAAC,MAAM,IAAIkY,OAAO,CAAC/M,EAAE,CAAC,EAAE;UACtBW,QAAQ,CAACqP,SAAS,CAAC,IAAI,CAAC;UACxBE,KAAK,CAAClQ,EAAE,CAAC;QACX,CAAC,MAAM,IACL,OAAOA,EAAE,KAAK,QAAQ,IACrB,OAAOA,EAAE,KAAK,QAAQ,KACpB,OAAOA,EAAE,CAAChL,IAAI,KAAK,QAAQ,IAAI,OAAOgL,EAAE,CAAClL,IAAI,KAAK,QAAQ,CAAE,EAC/D;UACA;UACAob,KAAK,CAAC5D,+BAA+B,CAAC5V,OAAO,EAAE7B,KAAK,CAAC,CAAC;UACtD,IAAI,OAAOmL,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAACpO,OAAO,EAAE;YACxC+O,QAAQ,CAAC/O,OAAO,CAACoO,EAAE,CAAC;UACtB,CAAC,MAAM;YACLW,QAAQ,CAAC/M,IAAI,CAACoM,EAAE,CAAC;UACnB;QACF,CAAC,MAAM;UACL;UACA3D,IAAI,CAAC2D,EAAE,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOtN,CAAC,EAAE;MACVwd,KAAK,CAACxd,CAAC,CAAC;IACV;EACF,CAAC;EAEDwa,QAAQ,CAACC,KAAK,EAAEyD,QAAQ,EAAE,YAAY;IACpC;IACA;IACA,IAAIC,WAAW,GAAGC,kBAAkB,CAACN,SAAS,CAAC;IAC/C,IAAIrD,KAAK,GAAG0D,WAAW,CAACvC,MAAM,CAAC3N,QAAQ,CAACjM,MAAM,CAACqc,YAAY,CAAC;IAC5D7D,QAAQ,CAACC,KAAK,EAAEyD,QAAQ,EAAE,YAAY;MACpC,IAAIjQ,QAAQ,CAAC8M,OAAO,KAAK5Y,KAAK,EAAE;QAC9B,OAAOqb,KAAK,CAACvD,8BAA8B,CAACjW,OAAO,EAAE7B,KAAK,CAAC,CAAC;MAC9D;MACA8L,QAAQ,CAAC8M,OAAO,GAAG,IAAI;MACvBkC,UAAU,CAAC9a,KAAK,CAAC;MACjB,IAAI8L,QAAQ,CAACjM,MAAM,CAAC8U,GAAG,EAAE;QACvB7I,QAAQ,CAACjM,MAAM,CAAC8U,GAAG,CAACG,SAAS,CAAC,YAAY;UACxC7S,kBAAkB,CAACjC,KAAK,CAAC;QAC3B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAEDka,OAAO,CAAC/T,SAAS,CAAC+U,WAAW,GAAG,SAASA,WAAWA,CAAElb,KAAK,EAAE;EAC3D,IAAI,CAAC6B,OAAO,GAAG7B,KAAK;EACpB,IAAI,CAACwY,EAAE,IAAI,IAAI,CAACA,EAAE,CAACxY,KAAK,CAAC;AAC3B,CAAC;AAEDka,OAAO,CAAC/T,SAAS,CAACgW,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;EAC5D;AAAA,CACD;AAEDjC,OAAO,CAAC/T,SAAS,CAACiW,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;EAChD;EACA;EACA,IAAI,CAAC5B,SAAS,CAACjc,OAAO,CAAC,UAAU8d,eAAe,EAAE;IAChDA,eAAe,CAAC,CAAC;EACnB,CAAC,CAAC;EACF,IAAI,CAAC7B,SAAS,GAAG,EAAE;;EAEnB;EACA;EACA,IAAI,CAAC3Y,OAAO,GAAGlB,KAAK;EACpB,IAAI,CAACiY,OAAO,GAAG,IAAI;AACrB,CAAC;AAED,SAASuB,aAAaA,CAAE/U,IAAI,EAAE;EAC5B,IAAI,CAACA,IAAI,EAAE;IACT,IAAI6K,SAAS,EAAE;MACb;MACA,IAAIqM,MAAM,GAAG1G,QAAQ,CAACe,aAAa,CAAC,MAAM,CAAC;MAC3CvR,IAAI,GAAIkX,MAAM,IAAIA,MAAM,CAACjO,YAAY,CAAC,MAAM,CAAC,IAAK,GAAG;MACrD;MACAjJ,IAAI,GAAGA,IAAI,CAACrI,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;IAC/C,CAAC,MAAM;MACLqI,IAAI,GAAG,GAAG;IACZ;EACF;EACA;EACA,IAAIA,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC1BH,IAAI,GAAG,GAAG,GAAGA,IAAI;EACnB;EACA;EACA,OAAOA,IAAI,CAACrI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAChC;AAEA,SAASye,YAAYA,CACnB3Z,OAAO,EACP2F,IAAI,EACJ;EACA,IAAIhG,CAAC;EACL,IAAI+a,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC1a,OAAO,CAACjD,MAAM,EAAE4I,IAAI,CAAC5I,MAAM,CAAC;EAC/C,KAAK4C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+a,GAAG,EAAE/a,CAAC,EAAE,EAAE;IACxB,IAAIK,OAAO,CAACL,CAAC,CAAC,KAAKgG,IAAI,CAAChG,CAAC,CAAC,EAAE;MAC1B;IACF;EACF;EACA,OAAO;IACLia,OAAO,EAAEjU,IAAI,CAAC1B,KAAK,CAAC,CAAC,EAAEtE,CAAC,CAAC;IACzBma,SAAS,EAAEnU,IAAI,CAAC1B,KAAK,CAACtE,CAAC,CAAC;IACxBka,WAAW,EAAE7Z,OAAO,CAACiE,KAAK,CAACtE,CAAC;EAC9B,CAAC;AACH;AAEA,SAASib,aAAaA,CACpBC,OAAO,EACPzc,IAAI,EACJ0c,IAAI,EACJC,OAAO,EACP;EACA,IAAIC,MAAM,GAAGhE,iBAAiB,CAAC6D,OAAO,EAAE,UAAU5D,GAAG,EAAE3W,QAAQ,EAAEqH,KAAK,EAAEnN,GAAG,EAAE;IAC3E,IAAIygB,KAAK,GAAGC,YAAY,CAACjE,GAAG,EAAE7Y,IAAI,CAAC;IACnC,IAAI6c,KAAK,EAAE;MACT,OAAO/e,KAAK,CAACC,OAAO,CAAC8e,KAAK,CAAC,GACvBA,KAAK,CAAC7e,GAAG,CAAC,UAAU6e,KAAK,EAAE;QAAE,OAAOH,IAAI,CAACG,KAAK,EAAE3a,QAAQ,EAAEqH,KAAK,EAAEnN,GAAG,CAAC;MAAE,CAAC,CAAC,GACzEsgB,IAAI,CAACG,KAAK,EAAE3a,QAAQ,EAAEqH,KAAK,EAAEnN,GAAG,CAAC;IACvC;EACF,CAAC,CAAC;EACF,OAAOmd,OAAO,CAACoD,OAAO,GAAGC,MAAM,CAACD,OAAO,CAAC,CAAC,GAAGC,MAAM,CAAC;AACrD;AAEA,SAASE,YAAYA,CACnBjE,GAAG,EACHzc,GAAG,EACH;EACA,IAAI,OAAOyc,GAAG,KAAK,UAAU,EAAE;IAC7B;IACAA,GAAG,GAAGtK,IAAI,CAACtS,MAAM,CAAC4c,GAAG,CAAC;EACxB;EACA,OAAOA,GAAG,CAAChZ,OAAO,CAACzD,GAAG,CAAC;AACzB;AAEA,SAASuf,kBAAkBA,CAAEF,WAAW,EAAE;EACxC,OAAOe,aAAa,CAACf,WAAW,EAAE,kBAAkB,EAAEsB,SAAS,EAAE,IAAI,CAAC;AACxE;AAEA,SAASlB,kBAAkBA,CAAEL,OAAO,EAAE;EACpC,OAAOgB,aAAa,CAAChB,OAAO,EAAE,mBAAmB,EAAEuB,SAAS,CAAC;AAC/D;AAEA,SAASA,SAASA,CAAEF,KAAK,EAAE3a,QAAQ,EAAE;EACnC,IAAIA,QAAQ,EAAE;IACZ,OAAO,SAAS8a,eAAeA,CAAA,EAAI;MACjC,OAAOH,KAAK,CAACpD,KAAK,CAACvX,QAAQ,EAAE8X,SAAS,CAAC;IACzC,CAAC;EACH;AACF;AAEA,SAASgC,kBAAkBA,CACzBN,SAAS,EACT;EACA,OAAOc,aAAa,CAClBd,SAAS,EACT,kBAAkB,EAClB,UAAUmB,KAAK,EAAEha,CAAC,EAAE0G,KAAK,EAAEnN,GAAG,EAAE;IAC9B,OAAO6gB,cAAc,CAACJ,KAAK,EAAEtT,KAAK,EAAEnN,GAAG,CAAC;EAC1C,CACF,CAAC;AACH;AAEA,SAAS6gB,cAAcA,CACrBJ,KAAK,EACLtT,KAAK,EACLnN,GAAG,EACH;EACA,OAAO,SAAS8gB,eAAeA,CAAEhS,EAAE,EAAEsJ,IAAI,EAAEjN,IAAI,EAAE;IAC/C,OAAOsV,KAAK,CAAC3R,EAAE,EAAEsJ,IAAI,EAAE,UAAU+D,EAAE,EAAE;MACnC,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;QAC5B,IAAI,CAAChP,KAAK,CAACnH,UAAU,CAAChG,GAAG,CAAC,EAAE;UAC1BmN,KAAK,CAACnH,UAAU,CAAChG,GAAG,CAAC,GAAG,EAAE;QAC5B;QACAmN,KAAK,CAACnH,UAAU,CAAChG,GAAG,CAAC,CAAC0C,IAAI,CAACyZ,EAAE,CAAC;MAChC;MACAhR,IAAI,CAACgR,EAAE,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;AACH;;AAEA;;AAEA,IAAI4E,YAAY,GAAG,aAAc,UAAUlD,OAAO,EAAE;EAClD,SAASkD,YAAYA,CAAEvd,MAAM,EAAEuF,IAAI,EAAE;IACnC8U,OAAO,CAAC9T,IAAI,CAAC,IAAI,EAAEvG,MAAM,EAAEuF,IAAI,CAAC;IAEhC,IAAI,CAACiY,cAAc,GAAGC,WAAW,CAAC,IAAI,CAAClY,IAAI,CAAC;EAC9C;EAEA,IAAK8U,OAAO,EAAGkD,YAAY,CAACG,SAAS,GAAGrD,OAAO;EAC/CkD,YAAY,CAACjX,SAAS,GAAGjH,MAAM,CAAC8K,MAAM,CAAEkQ,OAAO,IAAIA,OAAO,CAAC/T,SAAU,CAAC;EACtEiX,YAAY,CAACjX,SAAS,CAACqX,WAAW,GAAGJ,YAAY;EAEjDA,YAAY,CAACjX,SAAS,CAACgW,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IACjE,IAAIrQ,QAAQ,GAAG,IAAI;IAEnB,IAAI,IAAI,CAAC0O,SAAS,CAAC5b,MAAM,GAAG,CAAC,EAAE;MAC7B;IACF;IAEA,IAAIiB,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI4d,YAAY,GAAG5d,MAAM,CAACC,OAAO,CAAC+U,cAAc;IAChD,IAAI6I,cAAc,GAAG5G,iBAAiB,IAAI2G,YAAY;IAEtD,IAAIC,cAAc,EAAE;MAClB,IAAI,CAAClD,SAAS,CAACzb,IAAI,CAAC6U,WAAW,CAAC,CAAC,CAAC;IACpC;IAEA,IAAI+J,kBAAkB,GAAG,SAAAA,CAAA,EAAY;MACnC,IAAI9b,OAAO,GAAGiK,QAAQ,CAACjK,OAAO;;MAE9B;MACA;MACA,IAAIlC,QAAQ,GAAG2d,WAAW,CAACxR,QAAQ,CAAC1G,IAAI,CAAC;MACzC,IAAI0G,QAAQ,CAACjK,OAAO,KAAKlB,KAAK,IAAIhB,QAAQ,KAAKmM,QAAQ,CAACuR,cAAc,EAAE;QACtE;MACF;MAEAvR,QAAQ,CAAC+O,YAAY,CAAClb,QAAQ,EAAE,UAAUK,KAAK,EAAE;QAC/C,IAAI0d,cAAc,EAAE;UAClBlJ,YAAY,CAAC3U,MAAM,EAAEG,KAAK,EAAE6B,OAAO,EAAE,IAAI,CAAC;QAC5C;MACF,CAAC,CAAC;IACJ,CAAC;IACDqO,MAAM,CAACmE,gBAAgB,CAAC,UAAU,EAAEsJ,kBAAkB,CAAC;IACvD,IAAI,CAACnD,SAAS,CAACzb,IAAI,CAAC,YAAY;MAC9BmR,MAAM,CAACqE,mBAAmB,CAAC,UAAU,EAAEoJ,kBAAkB,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC;EAEDP,YAAY,CAACjX,SAAS,CAACyX,EAAE,GAAG,SAASA,EAAEA,CAAEC,CAAC,EAAE;IAC1C3N,MAAM,CAACZ,OAAO,CAACsO,EAAE,CAACC,CAAC,CAAC;EACtB,CAAC;EAEDT,YAAY,CAACjX,SAAS,CAACpH,IAAI,GAAG,SAASA,IAAIA,CAAEY,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,EAAE;IAC1E,IAAIjP,QAAQ,GAAG,IAAI;IAEnB,IAAIhL,GAAG,GAAG,IAAI;IACd,IAAIgd,SAAS,GAAGhd,GAAG,CAACe,OAAO;IAC3B,IAAI,CAACgZ,YAAY,CAAClb,QAAQ,EAAE,UAAUK,KAAK,EAAE;MAC3CkX,SAAS,CAAClR,SAAS,CAAC8F,QAAQ,CAAC1G,IAAI,GAAGpF,KAAK,CAACM,QAAQ,CAAC,CAAC;MACpDkU,YAAY,CAAC1I,QAAQ,CAACjM,MAAM,EAAEG,KAAK,EAAE8d,SAAS,EAAE,KAAK,CAAC;MACtDhD,UAAU,IAAIA,UAAU,CAAC9a,KAAK,CAAC;IACjC,CAAC,EAAE+a,OAAO,CAAC;EACb,CAAC;EAEDqC,YAAY,CAACjX,SAAS,CAACpJ,OAAO,GAAG,SAASA,OAAOA,CAAE4C,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,EAAE;IAChF,IAAIjP,QAAQ,GAAG,IAAI;IAEnB,IAAIhL,GAAG,GAAG,IAAI;IACd,IAAIgd,SAAS,GAAGhd,GAAG,CAACe,OAAO;IAC3B,IAAI,CAACgZ,YAAY,CAAClb,QAAQ,EAAE,UAAUK,KAAK,EAAE;MAC3CoU,YAAY,CAACpO,SAAS,CAAC8F,QAAQ,CAAC1G,IAAI,GAAGpF,KAAK,CAACM,QAAQ,CAAC,CAAC;MACvDkU,YAAY,CAAC1I,QAAQ,CAACjM,MAAM,EAAEG,KAAK,EAAE8d,SAAS,EAAE,KAAK,CAAC;MACtDhD,UAAU,IAAIA,UAAU,CAAC9a,KAAK,CAAC;IACjC,CAAC,EAAE+a,OAAO,CAAC;EACb,CAAC;EAEDqC,YAAY,CAACjX,SAAS,CAACgV,SAAS,GAAG,SAASA,SAASA,CAAEpc,IAAI,EAAE;IAC3D,IAAIue,WAAW,CAAC,IAAI,CAAClY,IAAI,CAAC,KAAK,IAAI,CAACvD,OAAO,CAACvB,QAAQ,EAAE;MACpD,IAAIuB,OAAO,GAAGmE,SAAS,CAAC,IAAI,CAACZ,IAAI,GAAG,IAAI,CAACvD,OAAO,CAACvB,QAAQ,CAAC;MAC1DvB,IAAI,GAAGmY,SAAS,CAACrV,OAAO,CAAC,GAAGuS,YAAY,CAACvS,OAAO,CAAC;IACnD;EACF,CAAC;EAEDub,YAAY,CAACjX,SAAS,CAAC4X,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAI;IACzE,OAAOT,WAAW,CAAC,IAAI,CAAClY,IAAI,CAAC;EAC/B,CAAC;EAED,OAAOgY,YAAY;AACrB,CAAC,CAAClD,OAAO,CAAE;AAEX,SAASoD,WAAWA,CAAElY,IAAI,EAAE;EAC1B,IAAIjF,IAAI,GAAG+P,MAAM,CAACvQ,QAAQ,CAACqe,QAAQ;EACnC,IAAIC,aAAa,GAAG9d,IAAI,CAAC+d,WAAW,CAAC,CAAC;EACtC,IAAIC,aAAa,GAAG/Y,IAAI,CAAC8Y,WAAW,CAAC,CAAC;EACtC;EACA;EACA;EACA,IAAI9Y,IAAI,KAAM6Y,aAAa,KAAKE,aAAa,IAC1CF,aAAa,CAAClc,OAAO,CAACiE,SAAS,CAACmY,aAAa,GAAG,GAAG,CAAC,CAAC,KAAK,CAAE,CAAC,EAAE;IAChEhe,IAAI,GAAGA,IAAI,CAAC2F,KAAK,CAACV,IAAI,CAACxG,MAAM,CAAC;EAChC;EACA,OAAO,CAACuB,IAAI,IAAI,GAAG,IAAI+P,MAAM,CAACvQ,QAAQ,CAACye,MAAM,GAAGlO,MAAM,CAACvQ,QAAQ,CAACS,IAAI;AACtE;;AAEA;;AAEA,IAAIie,WAAW,GAAG,aAAc,UAAUnE,OAAO,EAAE;EACjD,SAASmE,WAAWA,CAAExe,MAAM,EAAEuF,IAAI,EAAEkZ,QAAQ,EAAE;IAC5CpE,OAAO,CAAC9T,IAAI,CAAC,IAAI,EAAEvG,MAAM,EAAEuF,IAAI,CAAC;IAChC;IACA,IAAIkZ,QAAQ,IAAIC,aAAa,CAAC,IAAI,CAACnZ,IAAI,CAAC,EAAE;MACxC;IACF;IACAoZ,WAAW,CAAC,CAAC;EACf;EAEA,IAAKtE,OAAO,EAAGmE,WAAW,CAACd,SAAS,GAAGrD,OAAO;EAC9CmE,WAAW,CAAClY,SAAS,GAAGjH,MAAM,CAAC8K,MAAM,CAAEkQ,OAAO,IAAIA,OAAO,CAAC/T,SAAU,CAAC;EACrEkY,WAAW,CAAClY,SAAS,CAACqX,WAAW,GAAGa,WAAW;;EAE/C;EACA;EACAA,WAAW,CAAClY,SAAS,CAACgW,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAChE,IAAIrQ,QAAQ,GAAG,IAAI;IAEnB,IAAI,IAAI,CAAC0O,SAAS,CAAC5b,MAAM,GAAG,CAAC,EAAE;MAC7B;IACF;IAEA,IAAIiB,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAI4d,YAAY,GAAG5d,MAAM,CAACC,OAAO,CAAC+U,cAAc;IAChD,IAAI6I,cAAc,GAAG5G,iBAAiB,IAAI2G,YAAY;IAEtD,IAAIC,cAAc,EAAE;MAClB,IAAI,CAAClD,SAAS,CAACzb,IAAI,CAAC6U,WAAW,CAAC,CAAC,CAAC;IACpC;IAEA,IAAI+J,kBAAkB,GAAG,SAAAA,CAAA,EAAY;MACnC,IAAI9b,OAAO,GAAGiK,QAAQ,CAACjK,OAAO;MAC9B,IAAI,CAAC2c,WAAW,CAAC,CAAC,EAAE;QAClB;MACF;MACA1S,QAAQ,CAAC+O,YAAY,CAAC4D,OAAO,CAAC,CAAC,EAAE,UAAUze,KAAK,EAAE;QAChD,IAAI0d,cAAc,EAAE;UAClBlJ,YAAY,CAAC1I,QAAQ,CAACjM,MAAM,EAAEG,KAAK,EAAE6B,OAAO,EAAE,IAAI,CAAC;QACrD;QACA,IAAI,CAACiV,iBAAiB,EAAE;UACtB4H,WAAW,CAAC1e,KAAK,CAACM,QAAQ,CAAC;QAC7B;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAIqe,SAAS,GAAG7H,iBAAiB,GAAG,UAAU,GAAG,YAAY;IAC7D5G,MAAM,CAACmE,gBAAgB,CACrBsK,SAAS,EACThB,kBACF,CAAC;IACD,IAAI,CAACnD,SAAS,CAACzb,IAAI,CAAC,YAAY;MAC9BmR,MAAM,CAACqE,mBAAmB,CAACoK,SAAS,EAAEhB,kBAAkB,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC;EAEDU,WAAW,CAAClY,SAAS,CAACpH,IAAI,GAAG,SAASA,IAAIA,CAAEY,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,EAAE;IACzE,IAAIjP,QAAQ,GAAG,IAAI;IAEnB,IAAIhL,GAAG,GAAG,IAAI;IACd,IAAIgd,SAAS,GAAGhd,GAAG,CAACe,OAAO;IAC3B,IAAI,CAACgZ,YAAY,CACflb,QAAQ,EACR,UAAUK,KAAK,EAAE;MACf4e,QAAQ,CAAC5e,KAAK,CAACM,QAAQ,CAAC;MACxBkU,YAAY,CAAC1I,QAAQ,CAACjM,MAAM,EAAEG,KAAK,EAAE8d,SAAS,EAAE,KAAK,CAAC;MACtDhD,UAAU,IAAIA,UAAU,CAAC9a,KAAK,CAAC;IACjC,CAAC,EACD+a,OACF,CAAC;EACH,CAAC;EAEDsD,WAAW,CAAClY,SAAS,CAACpJ,OAAO,GAAG,SAASA,OAAOA,CAAE4C,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,EAAE;IAC/E,IAAIjP,QAAQ,GAAG,IAAI;IAEnB,IAAIhL,GAAG,GAAG,IAAI;IACd,IAAIgd,SAAS,GAAGhd,GAAG,CAACe,OAAO;IAC3B,IAAI,CAACgZ,YAAY,CACflb,QAAQ,EACR,UAAUK,KAAK,EAAE;MACf0e,WAAW,CAAC1e,KAAK,CAACM,QAAQ,CAAC;MAC3BkU,YAAY,CAAC1I,QAAQ,CAACjM,MAAM,EAAEG,KAAK,EAAE8d,SAAS,EAAE,KAAK,CAAC;MACtDhD,UAAU,IAAIA,UAAU,CAAC9a,KAAK,CAAC;IACjC,CAAC,EACD+a,OACF,CAAC;EACH,CAAC;EAEDsD,WAAW,CAAClY,SAAS,CAACyX,EAAE,GAAG,SAASA,EAAEA,CAAEC,CAAC,EAAE;IACzC3N,MAAM,CAACZ,OAAO,CAACsO,EAAE,CAACC,CAAC,CAAC;EACtB,CAAC;EAEDQ,WAAW,CAAClY,SAAS,CAACgV,SAAS,GAAG,SAASA,SAASA,CAAEpc,IAAI,EAAE;IAC1D,IAAI8C,OAAO,GAAG,IAAI,CAACA,OAAO,CAACvB,QAAQ;IACnC,IAAIme,OAAO,CAAC,CAAC,KAAK5c,OAAO,EAAE;MACzB9C,IAAI,GAAG6f,QAAQ,CAAC/c,OAAO,CAAC,GAAG6c,WAAW,CAAC7c,OAAO,CAAC;IACjD;EACF,CAAC;EAEDwc,WAAW,CAAClY,SAAS,CAAC4X,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAI;IACxE,OAAOU,OAAO,CAAC,CAAC;EAClB,CAAC;EAED,OAAOJ,WAAW;AACpB,CAAC,CAACnE,OAAO,CAAE;AAEX,SAASqE,aAAaA,CAAEnZ,IAAI,EAAE;EAC5B,IAAIzF,QAAQ,GAAG2d,WAAW,CAAClY,IAAI,CAAC;EAChC,IAAI,CAAC,MAAM,CAAC6D,IAAI,CAACtJ,QAAQ,CAAC,EAAE;IAC1BuQ,MAAM,CAACvQ,QAAQ,CAAC5C,OAAO,CAACiJ,SAAS,CAACZ,IAAI,GAAG,IAAI,GAAGzF,QAAQ,CAAC,CAAC;IAC1D,OAAO,IAAI;EACb;AACF;AAEA,SAAS6e,WAAWA,CAAA,EAAI;EACtB,IAAIre,IAAI,GAAGse,OAAO,CAAC,CAAC;EACpB,IAAIte,IAAI,CAACoF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC1B,OAAO,IAAI;EACb;EACAmZ,WAAW,CAAC,GAAG,GAAGve,IAAI,CAAC;EACvB,OAAO,KAAK;AACd;AAEA,SAASse,OAAOA,CAAA,EAAI;EAClB;EACA;EACA,IAAIxS,IAAI,GAAGiE,MAAM,CAACvQ,QAAQ,CAACsM,IAAI;EAC/B,IAAIhF,KAAK,GAAGgF,IAAI,CAAClK,OAAO,CAAC,GAAG,CAAC;EAC7B;EACA,IAAIkF,KAAK,GAAG,CAAC,EAAE;IAAE,OAAO,EAAE;EAAC;EAE3BgF,IAAI,GAAGA,IAAI,CAACnG,KAAK,CAACmB,KAAK,GAAG,CAAC,CAAC;EAE5B,OAAOgF,IAAI;AACb;AAEA,SAAS4S,MAAMA,CAAE1e,IAAI,EAAE;EACrB,IAAI8L,IAAI,GAAGiE,MAAM,CAACvQ,QAAQ,CAACsM,IAAI;EAC/B,IAAIzK,CAAC,GAAGyK,IAAI,CAAClK,OAAO,CAAC,GAAG,CAAC;EACzB,IAAIqD,IAAI,GAAG5D,CAAC,IAAI,CAAC,GAAGyK,IAAI,CAACnG,KAAK,CAAC,CAAC,EAAEtE,CAAC,CAAC,GAAGyK,IAAI;EAC3C,OAAQ7G,IAAI,GAAG,GAAG,GAAGjF,IAAI;AAC3B;AAEA,SAASye,QAAQA,CAAEze,IAAI,EAAE;EACvB,IAAI2W,iBAAiB,EAAE;IACrBI,SAAS,CAAC2H,MAAM,CAAC1e,IAAI,CAAC,CAAC;EACzB,CAAC,MAAM;IACL+P,MAAM,CAACvQ,QAAQ,CAACS,IAAI,GAAGD,IAAI;EAC7B;AACF;AAEA,SAASue,WAAWA,CAAEve,IAAI,EAAE;EAC1B,IAAI2W,iBAAiB,EAAE;IACrB1C,YAAY,CAACyK,MAAM,CAAC1e,IAAI,CAAC,CAAC;EAC5B,CAAC,MAAM;IACL+P,MAAM,CAACvQ,QAAQ,CAAC5C,OAAO,CAAC8hB,MAAM,CAAC1e,IAAI,CAAC,CAAC;EACvC;AACF;;AAEA;;AAEA,IAAI2e,eAAe,GAAG,aAAc,UAAU5E,OAAO,EAAE;EACrD,SAAS4E,eAAeA,CAAEjf,MAAM,EAAEuF,IAAI,EAAE;IACtC8U,OAAO,CAAC9T,IAAI,CAAC,IAAI,EAAEvG,MAAM,EAAEuF,IAAI,CAAC;IAChC,IAAI,CAACI,KAAK,GAAG,EAAE;IACf,IAAI,CAACyB,KAAK,GAAG,CAAC,CAAC;EACjB;EAEA,IAAKiT,OAAO,EAAG4E,eAAe,CAACvB,SAAS,GAAGrD,OAAO;EAClD4E,eAAe,CAAC3Y,SAAS,GAAGjH,MAAM,CAAC8K,MAAM,CAAEkQ,OAAO,IAAIA,OAAO,CAAC/T,SAAU,CAAC;EACzE2Y,eAAe,CAAC3Y,SAAS,CAACqX,WAAW,GAAGsB,eAAe;EAEvDA,eAAe,CAAC3Y,SAAS,CAACpH,IAAI,GAAG,SAASA,IAAIA,CAAEY,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,EAAE;IAC7E,IAAIjP,QAAQ,GAAG,IAAI;IAEnB,IAAI,CAAC+O,YAAY,CACflb,QAAQ,EACR,UAAUK,KAAK,EAAE;MACf8L,QAAQ,CAACtG,KAAK,GAAGsG,QAAQ,CAACtG,KAAK,CAACM,KAAK,CAAC,CAAC,EAAEgG,QAAQ,CAAC7E,KAAK,GAAG,CAAC,CAAC,CAACwS,MAAM,CAACzZ,KAAK,CAAC;MAC1E8L,QAAQ,CAAC7E,KAAK,EAAE;MAChB6T,UAAU,IAAIA,UAAU,CAAC9a,KAAK,CAAC;IACjC,CAAC,EACD+a,OACF,CAAC;EACH,CAAC;EAED+D,eAAe,CAAC3Y,SAAS,CAACpJ,OAAO,GAAG,SAASA,OAAOA,CAAE4C,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,EAAE;IACnF,IAAIjP,QAAQ,GAAG,IAAI;IAEnB,IAAI,CAAC+O,YAAY,CACflb,QAAQ,EACR,UAAUK,KAAK,EAAE;MACf8L,QAAQ,CAACtG,KAAK,GAAGsG,QAAQ,CAACtG,KAAK,CAACM,KAAK,CAAC,CAAC,EAAEgG,QAAQ,CAAC7E,KAAK,CAAC,CAACwS,MAAM,CAACzZ,KAAK,CAAC;MACtE8a,UAAU,IAAIA,UAAU,CAAC9a,KAAK,CAAC;IACjC,CAAC,EACD+a,OACF,CAAC;EACH,CAAC;EAED+D,eAAe,CAAC3Y,SAAS,CAACyX,EAAE,GAAG,SAASA,EAAEA,CAAEC,CAAC,EAAE;IAC7C,IAAI/R,QAAQ,GAAG,IAAI;IAEnB,IAAIiT,WAAW,GAAG,IAAI,CAAC9X,KAAK,GAAG4W,CAAC;IAChC,IAAIkB,WAAW,GAAG,CAAC,IAAIA,WAAW,IAAI,IAAI,CAACvZ,KAAK,CAAC5G,MAAM,EAAE;MACvD;IACF;IACA,IAAIoB,KAAK,GAAG,IAAI,CAACwF,KAAK,CAACuZ,WAAW,CAAC;IACnC,IAAI,CAAC9D,iBAAiB,CACpBjb,KAAK,EACL,YAAY;MACV,IAAIgb,IAAI,GAAGlP,QAAQ,CAACjK,OAAO;MAC3BiK,QAAQ,CAAC7E,KAAK,GAAG8X,WAAW;MAC5BjT,QAAQ,CAACoP,WAAW,CAAClb,KAAK,CAAC;MAC3B8L,QAAQ,CAACjM,MAAM,CAACub,UAAU,CAAC7c,OAAO,CAAC,UAAUkG,IAAI,EAAE;QACjDA,IAAI,IAAIA,IAAI,CAACzE,KAAK,EAAEgb,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,EACD,UAAU9d,GAAG,EAAE;MACb,IAAIib,mBAAmB,CAACjb,GAAG,EAAEka,qBAAqB,CAACI,UAAU,CAAC,EAAE;QAC9D1L,QAAQ,CAAC7E,KAAK,GAAG8X,WAAW;MAC9B;IACF,CACF,CAAC;EACH,CAAC;EAEDD,eAAe,CAAC3Y,SAAS,CAAC4X,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAI;IAC5E,IAAIlc,OAAO,GAAG,IAAI,CAAC2D,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC5G,MAAM,GAAG,CAAC,CAAC;IAC/C,OAAOiD,OAAO,GAAGA,OAAO,CAACvB,QAAQ,GAAG,GAAG;EACzC,CAAC;EAEDwe,eAAe,CAAC3Y,SAAS,CAACgV,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;IAC1D;EAAA,CACD;EAED,OAAO2D,eAAe;AACxB,CAAC,CAAC5E,OAAO,CAAE;;AAEX;;AAIA,IAAI8E,SAAS,GAAG,SAASA,SAASA,CAAElf,OAAO,EAAE;EAC3C,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;EAEtC,IAAI3C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCrB,IAAI,CAAC,IAAI,YAAYgjB,SAAS,EAAE,8CAA8C,CAAC;EACjF;EACA,IAAI,CAACrK,GAAG,GAAG,IAAI;EACf,IAAI,CAACsK,IAAI,GAAG,EAAE;EACd,IAAI,CAACnf,OAAO,GAAGA,OAAO;EACtB,IAAI,CAAC+b,WAAW,GAAG,EAAE;EACrB,IAAI,CAACK,YAAY,GAAG,EAAE;EACtB,IAAI,CAACd,UAAU,GAAG,EAAE;EACpB,IAAI,CAAC8D,OAAO,GAAGnN,aAAa,CAACjS,OAAO,CAACsQ,MAAM,IAAI,EAAE,EAAE,IAAI,CAAC;EAExD,IAAI+O,IAAI,GAAGrf,OAAO,CAACqf,IAAI,IAAI,MAAM;EACjC,IAAI,CAACb,QAAQ,GACXa,IAAI,KAAK,SAAS,IAAI,CAACrI,iBAAiB,IAAIhX,OAAO,CAACwe,QAAQ,KAAK,KAAK;EACxE,IAAI,IAAI,CAACA,QAAQ,EAAE;IACjBa,IAAI,GAAG,MAAM;EACf;EACA,IAAI,CAAClP,SAAS,EAAE;IACdkP,IAAI,GAAG,UAAU;EACnB;EACA,IAAI,CAACA,IAAI,GAAGA,IAAI;EAEhB,QAAQA,IAAI;IACV,KAAK,SAAS;MACZ,IAAI,CAAC7P,OAAO,GAAG,IAAI8N,YAAY,CAAC,IAAI,EAAEtd,OAAO,CAACsF,IAAI,CAAC;MACnD;IACF,KAAK,MAAM;MACT,IAAI,CAACkK,OAAO,GAAG,IAAI+O,WAAW,CAAC,IAAI,EAAEve,OAAO,CAACsF,IAAI,EAAE,IAAI,CAACkZ,QAAQ,CAAC;MACjE;IACF,KAAK,UAAU;MACb,IAAI,CAAChP,OAAO,GAAG,IAAIwP,eAAe,CAAC,IAAI,EAAEhf,OAAO,CAACsF,IAAI,CAAC;MACtD;IACF;MACE,IAAIjI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCzB,MAAM,CAAC,KAAK,EAAG,gBAAgB,GAAGujB,IAAK,CAAC;MAC1C;EACJ;AACF,CAAC;AAED,IAAIC,kBAAkB,GAAG;EAAEhN,YAAY,EAAE;IAAEiN,YAAY,EAAE;EAAK;AAAE,CAAC;AAEjEL,SAAS,CAAC7Y,SAAS,CAACqD,KAAK,GAAG,SAASA,KAAKA,CAAEc,GAAG,EAAEzI,OAAO,EAAEjC,cAAc,EAAE;EACxE,OAAO,IAAI,CAACsf,OAAO,CAAC1V,KAAK,CAACc,GAAG,EAAEzI,OAAO,EAAEjC,cAAc,CAAC;AACzD,CAAC;AAEDwf,kBAAkB,CAAChN,YAAY,CAAC3C,GAAG,GAAG,YAAY;EAChD,OAAO,IAAI,CAACH,OAAO,IAAI,IAAI,CAACA,OAAO,CAACzN,OAAO;AAC7C,CAAC;AAEDmd,SAAS,CAAC7Y,SAAS,CAACtB,IAAI,GAAG,SAASA,IAAIA,CAAE8P,GAAG,CAAC,8BAA8B;EACxE,IAAI7I,QAAQ,GAAG,IAAI;EAErB3O,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnCzB,MAAM,CACJ6S,OAAO,CAACE,SAAS,EACjB,wDAAwD,GACtD,gCACJ,CAAC;EAEH,IAAI,CAACsQ,IAAI,CAAClgB,IAAI,CAAC4V,GAAG,CAAC;;EAEnB;EACA;EACAA,GAAG,CAAC2K,KAAK,CAAC,gBAAgB,EAAE,YAAY;IACtC;IACA,IAAIrY,KAAK,GAAG6E,QAAQ,CAACmT,IAAI,CAACld,OAAO,CAAC4S,GAAG,CAAC;IACtC,IAAI1N,KAAK,GAAG,CAAC,CAAC,EAAE;MAAE6E,QAAQ,CAACmT,IAAI,CAACnO,MAAM,CAAC7J,KAAK,EAAE,CAAC,CAAC;IAAE;IAClD;IACA;IACA,IAAI6E,QAAQ,CAAC6I,GAAG,KAAKA,GAAG,EAAE;MAAE7I,QAAQ,CAAC6I,GAAG,GAAG7I,QAAQ,CAACmT,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;IAAE;IAErE,IAAI,CAACnT,QAAQ,CAAC6I,GAAG,EAAE;MAAE7I,QAAQ,CAACwD,OAAO,CAAC8M,QAAQ,CAAC,CAAC;IAAE;EACpD,CAAC,CAAC;;EAEF;EACA;EACA,IAAI,IAAI,CAACzH,GAAG,EAAE;IACZ;EACF;EAEA,IAAI,CAACA,GAAG,GAAGA,GAAG;EAEd,IAAIrF,OAAO,GAAG,IAAI,CAACA,OAAO;EAE1B,IAAIA,OAAO,YAAY8N,YAAY,IAAI9N,OAAO,YAAY+O,WAAW,EAAE;IACrE,IAAIkB,mBAAmB,GAAG,SAAAA,CAAUC,YAAY,EAAE;MAChD,IAAI/K,IAAI,GAAGnF,OAAO,CAACzN,OAAO;MAC1B,IAAI4b,YAAY,GAAG3R,QAAQ,CAAChM,OAAO,CAAC+U,cAAc;MAClD,IAAI6I,cAAc,GAAG5G,iBAAiB,IAAI2G,YAAY;MAEtD,IAAIC,cAAc,IAAI,UAAU,IAAI8B,YAAY,EAAE;QAChDhL,YAAY,CAAC1I,QAAQ,EAAE0T,YAAY,EAAE/K,IAAI,EAAE,KAAK,CAAC;MACnD;IACF,CAAC;IACD,IAAI0H,cAAc,GAAG,SAAAA,CAAUqD,YAAY,EAAE;MAC3ClQ,OAAO,CAAC6M,cAAc,CAAC,CAAC;MACxBoD,mBAAmB,CAACC,YAAY,CAAC;IACnC,CAAC;IACDlQ,OAAO,CAACuL,YAAY,CAClBvL,OAAO,CAACyO,kBAAkB,CAAC,CAAC,EAC5B5B,cAAc,EACdA,cACF,CAAC;EACH;EAEA7M,OAAO,CAACmL,MAAM,CAAC,UAAUza,KAAK,EAAE;IAC9B8L,QAAQ,CAACmT,IAAI,CAAC1gB,OAAO,CAAC,UAAUoW,GAAG,EAAE;MACnCA,GAAG,CAACjF,MAAM,GAAG1P,KAAK;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAEDgf,SAAS,CAAC7Y,SAAS,CAACsZ,UAAU,GAAG,SAASA,UAAUA,CAAElH,EAAE,EAAE;EACxD,OAAOmH,YAAY,CAAC,IAAI,CAAC7D,WAAW,EAAEtD,EAAE,CAAC;AAC3C,CAAC;AAEDyG,SAAS,CAAC7Y,SAAS,CAACwZ,aAAa,GAAG,SAASA,aAAaA,CAAEpH,EAAE,EAAE;EAC9D,OAAOmH,YAAY,CAAC,IAAI,CAACxD,YAAY,EAAE3D,EAAE,CAAC;AAC5C,CAAC;AAEDyG,SAAS,CAAC7Y,SAAS,CAACyZ,SAAS,GAAG,SAASA,SAASA,CAAErH,EAAE,EAAE;EACtD,OAAOmH,YAAY,CAAC,IAAI,CAACtE,UAAU,EAAE7C,EAAE,CAAC;AAC1C,CAAC;AAEDyG,SAAS,CAAC7Y,SAAS,CAACuU,OAAO,GAAG,SAASA,OAAOA,CAAElC,EAAE,EAAEmC,OAAO,EAAE;EAC3D,IAAI,CAACrL,OAAO,CAACoL,OAAO,CAAClC,EAAE,EAAEmC,OAAO,CAAC;AACnC,CAAC;AAEDqE,SAAS,CAAC7Y,SAAS,CAACyU,OAAO,GAAG,SAASA,OAAOA,CAAED,OAAO,EAAE;EACvD,IAAI,CAACrL,OAAO,CAACsL,OAAO,CAACD,OAAO,CAAC;AAC/B,CAAC;AAEDqE,SAAS,CAAC7Y,SAAS,CAACpH,IAAI,GAAG,SAASA,IAAIA,CAAEY,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,EAAE;EACrE,IAAIjP,QAAQ,GAAG,IAAI;;EAErB;EACA,IAAI,CAACgP,UAAU,IAAI,CAACC,OAAO,IAAI,OAAO8E,OAAO,KAAK,WAAW,EAAE;IAC7D,OAAO,IAAIA,OAAO,CAAC,UAAU7T,OAAO,EAAEoN,MAAM,EAAE;MAC5CtN,QAAQ,CAACwD,OAAO,CAACvQ,IAAI,CAACY,QAAQ,EAAEqM,OAAO,EAAEoN,MAAM,CAAC;IAClD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAI,CAAC9J,OAAO,CAACvQ,IAAI,CAACY,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,CAAC;EAClD;AACF,CAAC;AAEDiE,SAAS,CAAC7Y,SAAS,CAACpJ,OAAO,GAAG,SAASA,OAAOA,CAAE4C,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,EAAE;EAC3E,IAAIjP,QAAQ,GAAG,IAAI;;EAErB;EACA,IAAI,CAACgP,UAAU,IAAI,CAACC,OAAO,IAAI,OAAO8E,OAAO,KAAK,WAAW,EAAE;IAC7D,OAAO,IAAIA,OAAO,CAAC,UAAU7T,OAAO,EAAEoN,MAAM,EAAE;MAC5CtN,QAAQ,CAACwD,OAAO,CAACvS,OAAO,CAAC4C,QAAQ,EAAEqM,OAAO,EAAEoN,MAAM,CAAC;IACrD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAI,CAAC9J,OAAO,CAACvS,OAAO,CAAC4C,QAAQ,EAAEmb,UAAU,EAAEC,OAAO,CAAC;EACrD;AACF,CAAC;AAEDiE,SAAS,CAAC7Y,SAAS,CAACyX,EAAE,GAAG,SAASA,EAAEA,CAAEC,CAAC,EAAE;EACvC,IAAI,CAACvO,OAAO,CAACsO,EAAE,CAACC,CAAC,CAAC;AACpB,CAAC;AAEDmB,SAAS,CAAC7Y,SAAS,CAAC2Z,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;EAC1C,IAAI,CAAClC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AAEDoB,SAAS,CAAC7Y,SAAS,CAAC4Z,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;EAChD,IAAI,CAACnC,EAAE,CAAC,CAAC,CAAC;AACZ,CAAC;AAEDoB,SAAS,CAAC7Y,SAAS,CAAC6Z,oBAAoB,GAAG,SAASA,oBAAoBA,CAAE7U,EAAE,EAAE;EAC5E,IAAInL,KAAK,GAAGmL,EAAE,GACVA,EAAE,CAAC3K,OAAO,GACR2K,EAAE,GACF,IAAI,CAACa,OAAO,CAACb,EAAE,CAAC,CAACnL,KAAK,GACxB,IAAI,CAACoS,YAAY;EACrB,IAAI,CAACpS,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EACA,OAAO,EAAE,CAACyZ,MAAM,CAACC,KAAK,CACpB,EAAE,EACF1Z,KAAK,CAACQ,OAAO,CAACvC,GAAG,CAAC,UAAUoJ,CAAC,EAAE;IAC7B,OAAOnI,MAAM,CAACC,IAAI,CAACkI,CAAC,CAAC/C,UAAU,CAAC,CAACrG,GAAG,CAAC,UAAU5B,GAAG,EAAE;MAClD,OAAOgL,CAAC,CAAC/C,UAAU,CAACjI,GAAG,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CACH,CAAC;AACH,CAAC;AAED2iB,SAAS,CAAC7Y,SAAS,CAAC6F,OAAO,GAAG,SAASA,OAAOA,CAC5Cb,EAAE,EACFtJ,OAAO,EACPwD,MAAM,EACN;EACAxD,OAAO,GAAGA,OAAO,IAAI,IAAI,CAACyN,OAAO,CAACzN,OAAO;EACzC,IAAIlC,QAAQ,GAAG0K,iBAAiB,CAACc,EAAE,EAAEtJ,OAAO,EAAEwD,MAAM,EAAE,IAAI,CAAC;EAC3D,IAAIrF,KAAK,GAAG,IAAI,CAACwJ,KAAK,CAAC7J,QAAQ,EAAEkC,OAAO,CAAC;EACzC,IAAIvB,QAAQ,GAAGN,KAAK,CAACJ,cAAc,IAAII,KAAK,CAACM,QAAQ;EACrD,IAAI8E,IAAI,GAAG,IAAI,CAACkK,OAAO,CAAClK,IAAI;EAC5B,IAAI6G,IAAI,GAAGgU,UAAU,CAAC7a,IAAI,EAAE9E,QAAQ,EAAE,IAAI,CAAC6e,IAAI,CAAC;EAChD,OAAO;IACLxf,QAAQ,EAAEA,QAAQ;IAClBK,KAAK,EAAEA,KAAK;IACZiM,IAAI,EAAEA,IAAI;IACV;IACAiU,YAAY,EAAEvgB,QAAQ;IACtBwZ,QAAQ,EAAEnZ;EACZ,CAAC;AACH,CAAC;AAEDgf,SAAS,CAAC7Y,SAAS,CAACgM,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;EACpD,OAAO,IAAI,CAAC+M,OAAO,CAAC/M,SAAS,CAAC,CAAC;AACjC,CAAC;AAED6M,SAAS,CAAC7Y,SAAS,CAAC8L,QAAQ,GAAG,SAASA,QAAQA,CAAEC,aAAa,EAAElS,KAAK,EAAE;EACtE,IAAI,CAACkf,OAAO,CAACjN,QAAQ,CAACC,aAAa,EAAElS,KAAK,CAAC;EAC3C,IAAI,IAAI,CAACsP,OAAO,CAACzN,OAAO,KAAKlB,KAAK,EAAE;IAClC,IAAI,CAAC2O,OAAO,CAACuL,YAAY,CAAC,IAAI,CAACvL,OAAO,CAACyO,kBAAkB,CAAC,CAAC,CAAC;EAC9D;AACF,CAAC;AAEDiB,SAAS,CAAC7Y,SAAS,CAAC6L,SAAS,GAAG,SAASA,SAASA,CAAE5B,MAAM,EAAE;EAC1D,IAAIjT,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCrB,IAAI,CAAC,KAAK,EAAE,uGAAuG,CAAC;EACtH;EACA,IAAI,CAACkjB,OAAO,CAAClN,SAAS,CAAC5B,MAAM,CAAC;EAC9B,IAAI,IAAI,CAACd,OAAO,CAACzN,OAAO,KAAKlB,KAAK,EAAE;IAClC,IAAI,CAAC2O,OAAO,CAACuL,YAAY,CAAC,IAAI,CAACvL,OAAO,CAACyO,kBAAkB,CAAC,CAAC,CAAC;EAC9D;AACF,CAAC;AAED7e,MAAM,CAACihB,gBAAgB,CAAEnB,SAAS,CAAC7Y,SAAS,EAAEiZ,kBAAmB,CAAC;AAElE,IAAIgB,WAAW,GAAGpB,SAAS;AAE3B,SAASU,YAAYA,CAAEW,IAAI,EAAE9H,EAAE,EAAE;EAC/B8H,IAAI,CAACthB,IAAI,CAACwZ,EAAE,CAAC;EACb,OAAO,YAAY;IACjB,IAAI/W,CAAC,GAAG6e,IAAI,CAACte,OAAO,CAACwW,EAAE,CAAC;IACxB,IAAI/W,CAAC,GAAG,CAAC,CAAC,EAAE;MAAE6e,IAAI,CAACvP,MAAM,CAACtP,CAAC,EAAE,CAAC,CAAC;IAAE;EACnC,CAAC;AACH;AAEA,SAASye,UAAUA,CAAE7a,IAAI,EAAE9E,QAAQ,EAAE6e,IAAI,EAAE;EACzC,IAAIhf,IAAI,GAAGgf,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG7e,QAAQ,GAAGA,QAAQ;EACtD,OAAO8E,IAAI,GAAGY,SAAS,CAACZ,IAAI,GAAG,GAAG,GAAGjF,IAAI,CAAC,GAAGA,IAAI;AACnD;;AAEA;AACA6e,SAAS,CAACvQ,OAAO,GAAGA,OAAO;AAC3BuQ,SAAS,CAACsB,OAAO,GAAG,OAAO;AAC3BtB,SAAS,CAAC7G,mBAAmB,GAAGA,mBAAmB;AACnD6G,SAAS,CAAC5H,qBAAqB,GAAGA,qBAAqB;AACvD4H,SAAS,CAACuB,cAAc,GAAG5f,KAAK;AAEhC,IAAIsP,SAAS,IAAIC,MAAM,CAACxB,GAAG,EAAE;EAC3BwB,MAAM,CAACxB,GAAG,CAAC8R,GAAG,CAACxB,SAAS,CAAC;AAC3B;AAEA,IAAIsB,OAAO,GAAG,OAAO;AAErB,SAASlJ,qBAAqB,EAAElM,IAAI,IAAIuV,UAAU,EAAEje,IAAI,IAAIke,UAAU,EAAE/f,KAAK,IAAI4f,cAAc,EAAEH,WAAW,IAAIxd,OAAO,EAAEuV,mBAAmB,EAAEmI,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}